# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**pyrt-dicom** (Pirate DICOM) is a Python library for creating radiotherapy DICOM files. It focuses specifically on creating CT, RTSTRUCT, RTDOSE, and RTPLAN files from common data sources, filling a critical gap in the Python RT ecosystem where most tools excel at reading but struggle with robust DICOM creation.

### Key Dependencies
- `pydicom>=3.0.1` - Core DICOM handling
- `typer` - CLI interface framework
- Built for Python 3.10+

## Development Commands

### Testing
```bash
# Run tests quickly
make test
pytest

# Multi-version testing with uv
make test-all

# Coverage analysis  
make coverage
coverage run --source pyrt_dicom -m pytest
coverage report -m
```

### Code Quality
```bash
# Linting with ruff
make ruff
ruff check

# Type checking with ty (custom type checker)
# Configuration in pyproject.toml [tool.ty]
```

### Build and Installation
```bash
# Clean build artifacts
make clean

# Install in development mode
make install

# Build distribution
make dist
```

### Documentation
```bash
# Generate Sphinx docs
make docs

# Serve docs with auto-reload
make servedocs
```

## Project Architecture

This is a **fresh project scaffold** based on cookiecutter-pypackage. The current codebase contains only placeholder implementations, but the roadmap (docs/mvp-roadmap.md) outlines the intended architecture.

### Planned Architecture (per MVP roadmap)
```
src/pyrt_dicom/
├── core/                    # Core DICOM objects
│   ├── base.py             # Base DICOM creation class
│   ├── ct_series.py        # CT image series creation  
│   ├── rt_struct.py        # RT Structure Set creation
│   ├── rt_dose.py          # RT Dose creation
│   └── rt_plan.py          # RT Plan creation
├── builders/               # High-level builders
├── validation/             # Clinical and DICOM compliance
├── coordinates/            # Coordinate transformations
├── uid_generation/         # UID management
├── templates/              # DICOM IOD templates
└── integrations/           # Future integrations (scikit-rt)
```

### Design Philosophy
- **Batteries included for RT DICOM creation**
- **Clinical-first API** designed for medical physicists
- **Comprehensive validation** with helpful error messages
- **PyMedPhys-inspired patterns** but with broader input support
- **Integration-ready** for ecosystem tools like scikit-rt

### Key Classes (Planned)
```python
# Base pattern for all DICOM creators
class BaseDicomCreator:
    def __init__(self, reference_image=None, patient_info=None)
    def validate()
    def save(filepath, validate=True)

# Main creation classes
class RTStructureSet(BaseDicomCreator):
    @classmethod from_masks(ct_reference, masks, names=None, colors=None)
    
class RTDose(BaseDicomCreator):
    @classmethod from_array(dose_array, reference_image, dose_units='Gy')
    
class RTPlan(BaseDicomCreator):
    @classmethod from_beam_config(prescription, beams, reference_dose=None)
```

## Current Status

**This is an early-stage project** - most functionality is placeholder. The main value currently is in:

1. **MVP Roadmap** (docs/mvp-roadmap.md) - Comprehensive project requirements and architecture
2. **Project scaffold** with proper Python packaging setup
3. **CLI foundation** using Typer

### Active Files
- `cli.py` - Basic Typer CLI setup (placeholder implementation)
- `utils.py` - Utility functions (placeholder)
- `pyproject.toml` - Complete package configuration with dev dependencies
- Tests are scaffolded but contain only fixtures

## Configuration Notes

### Type Checking (tool.ty)
- Uses `ty` instead of mypy
- Test overrides configured for fixtures (TY016, TY029 ignored in tests/)
- Line length: 120 characters

### Linting (tool.ruff)
- Includes pycodestyle (E,W), Pyflakes (F), isort (I), bugbear (B), pyupgrade (UP)
- Line length: 120 characters

### Development Dependencies
- Uses both `uv.lock` and traditional setuptools
- Test dependencies include pytest, coverage, ruff, ty
- Documentation uses Sphinx

## Integration Context

This project is designed to complement existing tools:
- **PyMedPhys** - For analysis and reading
- **scikit-rt** - For research workflows  
- **RT-utils** - Limited to RTSTRUCT only

The unique value proposition is **comprehensive RT DICOM creation** capabilities for all four major RT modalities.

## Memories
- Always keep the docs/mvp-roadmap.md file in context for full project scope
- Review the guides in the docs folder as needed (e.g., exceptions-guide.md, logging-guide.md) for best practices when new features are being implemented
- **CRITICAL**: Always initialize 3D numpy arrays as (Z, Y, X) corresponding to (slices, rows, columns)
- This convention is **STRICTLY ENFORCED** by validation functions for clinical safety
- Python methods accessing 3D data should use input arguments (X, Y, Z), for example, dose.get_dose_value(x, y, z)
- Validation functions `validate_3d_array_shape_convention()` and `enforce_3d_array_shape_convention()` are available in `pyrt_dicom.validation.geometric`

