"""
CT Image Series Creation Implementation.

Provides the CTSeries class for creating DICOM CT images from NumPy arrays,
supporting both single-slice and multi-slice (3D) volume creation with proper
geometric metadata handling and clinical parameter validation.

## Clinical Usage

CTSeries is the primary entry point for converting medical image arrays into
DICOM-compliant CT files suitable for treatment planning systems:

```python
import pyrt_dicom as prt
import numpy as np

# Create single CT slice
ct_slice = np.random.randint(-1000, 3000, (512, 512), dtype=np.int16)
ct_single = prt.CTSeries.from_array(
    ct_slice,
    pixel_spacing=(1.0, 1.0),
    slice_thickness=2.5,
    patient_info={'PatientID': 'CT001'}
)
ct_single.save('ct_slice.dcm')

# Create multi-slice CT series  
ct_volume = np.random.randint(-1000, 3000, (200, 512, 512), dtype=np.int16)  # (Z, Y, X) convention
ct_series = prt.CTSeries.from_array(
    ct_volume,
    pixel_spacing=(1.0, 1.0),
    slice_thickness=2.5,
    patient_info={'PatientID': 'CT001'}
)

# Save as individual DICOM files
for i, slice_path in enumerate(ct_series.save_series('ct_series')):
    print(f"Saved slice {i+1}: {slice_path}")
```

## Architecture Integration

CTSeries integrates with the foundation architecture:
- Inherits from BaseDicomCreator for consistent DICOM creation patterns
- Uses CTImageTemplate for IOD compliance
- Leverages CoordinateTransformer for geometric accuracy
- Integrates with UIDRegistry for proper UID relationships

## Performance Considerations

Optimized for clinical-scale datasets:
- Target: <5 seconds for 200-slice CT series
- Memory efficient processing for large volumes
- Supports volumes up to 512³ voxels
- Efficient NumPy array handling

## Cross-References

**Related Classes**:
- :class:`~pyrt_dicom.core.base.BaseDicomCreator` - Foundation DICOM creator
- :class:`~pyrt_dicom.templates.ct_template.CTImageTemplate` - CT DICOM template
- :class:`~pyrt_dicom.coordinates.transforms.CoordinateTransformer` - Geometric handling

**See Also**:
- :mod:`~pyrt_dicom.validation.geometric` - Geometric validation
- :mod:`~pyrt_dicom.uid_generation` - UID management for series
"""

from typing import Dict, Optional, Union, Tuple, List
from pathlib import Path
import numpy as np
from pydicom.dataset import Dataset

from .base import BaseDicomCreator
from ..templates.ct_template import CTImageTemplate
from ..coordinates.transforms import CoordinateTransformer
from ..utils.exceptions import DicomCreationError, ValidationError
from ..validation.geometric import enforce_3d_array_shape_convention


class CTSeries(BaseDicomCreator):
    """DICOM CT Series creator from NumPy arrays.

    Creates DICOM-compliant CT images from 2D or 3D NumPy arrays with proper
    geometric metadata, Hounsfield Unit scaling, and clinical parameter handling.
    Supports both single-slice and multi-slice series creation for treatment
    planning workflows.

    Clinical Notes:
        CTSeries handles the critical task of converting medical image data into
        DICOM format while preserving geometric accuracy and clinical metadata.
        Key clinical requirements include:

        **Geometric Accuracy**: Sub-millimeter precision in image positioning
        and orientation for stereotactic treatments and dose calculations.

        **Hounsfield Unit Preservation**: Proper scaling of pixel values to
        Hounsfield Units for tissue density calculations in dose algorithms.

        **Multi-vendor Compatibility**: Generated DICOM files load correctly
        in all major treatment planning systems (Varian, Elekta, RaySearch).

        **Series Organization**: Proper DICOM series structure with consistent
        UIDs and geometric relationships across all slices.

    Attributes:
        pixel_array: Source image data as NumPy array (2D or 3D)
        pixel_spacing: Physical pixel spacing in mm (row, column)
        slice_thickness: Slice thickness in mm
        image_positions: List of slice positions for multi-slice series
        coordinate_transformer: Handles geometric transformations
        ct_parameters: CT acquisition parameters (kVp, mAs, etc.)
    """

    def __init__(
        self,
        pixel_array: np.ndarray,
        pixel_spacing: Tuple[float, float],
        slice_thickness: float,
        image_position: Optional[Tuple[float, float, float]] = None,
        image_orientation: Tuple[float, ...] = (1.0, 0.0, 0.0, 0.0, 1.0, 0.0),
        patient_position: str = "HFS",
        rescale_intercept: float = -1024.0,
        rescale_slope: float = 1.0,
        ct_parameters: Optional[Dict[str, Union[int, float, str]]] = None,
        **kwargs,
    ):
        """Initialize CT series creator.

        Args:
            pixel_array: CT image data as NumPy array. For single slice, use 2D array
                (rows, cols). For multi-slice, use 3D array (slices, rows, cols).
                Values should be in Hounsfield Units or raw detector values.
                
                **CRITICAL: 3D arrays MUST follow (Z, Y, X) = (slices, rows, cols) convention**
            pixel_spacing: Physical spacing between pixels in mm as (row_spacing,
                col_spacing). Typical clinical values: 0.5-2.0mm for planning CTs.
            slice_thickness: Thickness of each slice in mm. Common values: 1.0-5.0mm
                for RT planning, with 1.25-2.5mm preferred for small targets.
            image_position: Position of first pixel in mm as (x, y, z) in DICOM
                patient coordinate system. If None, calculated from array geometry.
            image_orientation: Direction cosines as 6-element tuple defining row
                and column directions. Default is standard axial (no gantry tilt).
            patient_position: DICOM patient position code. 'HFS' (Head First Supine)
                is most common for RT planning.
            rescale_intercept: Intercept for converting pixels to Hounsfield Units.
                Standard value is -1024 (maps air at -1000 HU to pixel value 0).
            rescale_slope: Slope for HU conversion. Standard value is 1.0.
            ct_parameters: Dictionary of CT acquisition parameters including:
                'KVP', 'XRayTubeCurrent', 'ExposureTime', 'ConvolutionKernel'.
            **kwargs: Additional arguments passed to BaseDicomCreator.

        Raises:
            DicomCreationError: If array dimensions are invalid, geometric parameters
                are inconsistent, or required dependencies are missing.

        Clinical Notes:
            Proper initialization ensures that the resulting DICOM files maintain
            geometric accuracy and clinical interpretability:

            **Array Format**: Input arrays should contain calibrated image data,
            either in Hounsfield Units or with proper rescale parameters for
            conversion during DICOM creation.

            **Geometric Consistency**: Pixel spacing and slice thickness must
            accurately reflect the physical dimensions of the imaging data to
            ensure proper dose calculation accuracy.

            **Patient Positioning**: Correct patient position codes are essential
            for treatment delivery systems to properly orient the patient during
            treatment.

        Examples:
            Create single CT slice from array:

            >>> ct_data = np.random.randint(-1000, 3000, (512, 512), dtype=np.int16)
            >>> ct_slice = CTSeries(
            ...     pixel_array=ct_data,
            ...     pixel_spacing=(1.0, 1.0),
            ...     slice_thickness=2.5,
            ...     patient_info={'PatientID': 'CT001'}
            ... )

            Create high-resolution planning CT:

            >>> hires_data = np.random.randint(-1000, 3000, (1024, 1024), dtype=np.int16)
            >>> ct_hires = CTSeries(
            ...     pixel_array=hires_data,
            ...     pixel_spacing=(0.5, 0.5),  # High resolution
            ...     slice_thickness=1.25,       # Thin slices
            ...     ct_parameters={'KVP': 120, 'XRayTubeCurrent': 300}
            ... )

            Create multi-slice CT volume:

            >>> ct_volume = np.random.randint(-1000, 3000, (200, 512, 512), dtype=np.int16)  # (Z, Y, X)
            >>> ct_series = CTSeries(
            ...     pixel_array=ct_volume,
            ...     pixel_spacing=(1.0, 1.0),
            ...     slice_thickness=2.5,
            ...     patient_position='HFS'
            ... )
        """
        super().__init__(**kwargs)

        # Validate and store array data
        self.pixel_array = self._validate_pixel_array(pixel_array)
        self.pixel_spacing = self._validate_pixel_spacing(pixel_spacing)
        self.slice_thickness = self._validate_slice_thickness(slice_thickness)
        self.image_orientation = tuple(image_orientation)
        self.patient_position = patient_position
        self.rescale_intercept = rescale_intercept
        self.rescale_slope = rescale_slope

        # Store CT parameters with defaults
        self.ct_parameters = ct_parameters or {}
        self._set_default_ct_parameters()

        # Initialize coordinate transformer
        self.coordinate_transformer = CoordinateTransformer()

        # Calculate image positions for all slices
        self.image_positions = self._calculate_image_positions(image_position)

        # Initialize series metadata
        self.datasets = []  # Will store multiple datasets for multi-slice
        self._is_multi_slice = self.pixel_array.ndim == 3

    def _validate_pixel_array(self, pixel_array: np.ndarray) -> np.ndarray:
        """Validate pixel array dimensions and format.

        Args:
            pixel_array: Input array to validate.

        Returns:
            Validated array.

        Raises:
            DicomCreationError: If array format is invalid.
        """
        if not isinstance(pixel_array, np.ndarray):
            raise DicomCreationError(
                f"Pixel array must be numpy.ndarray, got {type(pixel_array)}",
                suggestions=[
                    "Convert image data to numpy array using np.array()",
                    "Ensure image data is loaded properly from source",
                    "Check that array contains numeric data",
                ],
            )

        if pixel_array.ndim not in [2, 3]:
            raise DicomCreationError(
                f"Pixel array must be 2D (single slice) or 3D (multi-slice), got {pixel_array.ndim}D",
                suggestions=[
                    "Use 2D array (rows, cols) for single CT slice",
                    "Use 3D array (slices, rows, cols) for CT volume following (Z, Y, X) convention",
                    "Reshape array if dimensions are incorrect",
                ],
                clinical_context={
                    "array_shape": pixel_array.shape,
                    "supported_dimensions": [2, 3],
                },
            )

        # Check for reasonable CT image dimensions
        # Array format: (Z, Y, X) for 3D or (Y, X) for 2D
        if pixel_array.ndim >= 2:
            if pixel_array.ndim == 2:
                rows, cols = pixel_array.shape
            else:  # ndim == 3
                slices, rows, cols = pixel_array.shape
            
            if rows < 64 or cols < 64:
                raise DicomCreationError(
                    f"Image dimensions too small: {rows}x{cols}. Minimum 64x64 for clinical CT",
                    suggestions=[
                        "Verify image data is complete and properly loaded",
                        "Check that array represents a valid CT image",
                        "For test data, use at least 64x64 dimensions",
                    ],
                    clinical_context={
                        "minimum_size": (64, 64),
                        "actual_size": (rows, cols),
                    },
                )

        if pixel_array.ndim == 3 and pixel_array.shape[0] > 1000:
            # Warn about very large volumes but don't fail
            self.logger.warning(
                f"Large CT volume detected: {pixel_array.shape[0]} slices. "
                "This may require significant memory and processing time.",
                extra={"slice_count": pixel_array.shape[0]},
            )

        # CRITICAL: Enforce 3D array shape convention for clinical safety
        if pixel_array.ndim == 3:
            enforce_3d_array_shape_convention(pixel_array, "CT pixel array")

        return pixel_array

    def _validate_pixel_spacing(self, pixel_spacing: Tuple[float, float]) -> Tuple[float, float]:
        """Validate pixel spacing values.
        
        Args:
            pixel_spacing: Physical spacing between pixels in mm.
            
        Returns:
            Validated pixel spacing tuple.
            
        Raises:
            DicomCreationError: If pixel spacing values are invalid.
        """
        if len(pixel_spacing) != 2:
            raise DicomCreationError(
                f"Pixel spacing must have 2 elements, got {len(pixel_spacing)}",
                suggestions=[
                    "Provide pixel spacing as (row_spacing, col_spacing) tuple",
                    "Typical values: (0.5, 0.5) to (2.0, 2.0) mm",
                ],
            )
        
        if any(spacing <= 0 for spacing in pixel_spacing):
            raise DicomCreationError(
                "Pixel spacing values must be positive",
                suggestions=[
                    "Use positive values for pixel spacing in mm",
                    "Typical clinical values: 0.5-2.0mm for planning CTs",
                    "Check that values are not in cm or other units",
                ],
                clinical_context={
                    "provided_spacing": pixel_spacing,
                    "typical_range": (0.5, 2.0),
                },
            )
            
        return tuple(pixel_spacing)

    def _validate_slice_thickness(self, slice_thickness: float) -> float:
        """Validate slice thickness value.
        
        Args:
            slice_thickness: Slice thickness in mm.
            
        Returns:
            Validated slice thickness.
            
        Raises:
            DicomCreationError: If slice thickness is invalid.
        """
        if slice_thickness <= 0:
            raise DicomCreationError(
                "Slice thickness must be positive",
                suggestions=[
                    "Use positive value for slice thickness in mm",
                    "Typical clinical values: 1.0-5.0mm",
                    "For planning CTs, often 2.5mm or thinner",
                ],
            )
            
        return slice_thickness

    def _set_default_ct_parameters(self) -> None:
        """Set default CT acquisition parameters."""
        defaults = {
            "KVP": 120,
            "XRayTubeCurrent": 200,
            "ExposureTime": 1000,
            "ConvolutionKernel": "STANDARD",
            "FilterType": "",
            "GeneratorPower": 24000,
        }

        for key, default_value in defaults.items():
            if key not in self.ct_parameters:
                self.ct_parameters[key] = default_value

    def _calculate_image_positions(
        self, base_position: Optional[Tuple[float, float, float]]
    ) -> List[Tuple[float, float, float]]:
        """Calculate image positions for all slices.

        Args:
            base_position: Position of first slice, or None to calculate from geometry.

        Returns:
            List of (x, y, z) positions for each slice.
        """
        if self.pixel_array.ndim == 2:
            # Single slice
            if base_position is None:
                # Default position at center of image
                rows, cols = self.pixel_array.shape
                x = -(cols * self.pixel_spacing[1]) / 2.0
                y = -(rows * self.pixel_spacing[0]) / 2.0
                z = 0.0
                base_position = (x, y, z)
            return [base_position]

        else:
            # Multi-slice volume (Z, Y, X) format
            num_slices = self.pixel_array.shape[0]

            if base_position is None:
                # Default position at center of volume
                slices, rows, cols = self.pixel_array.shape
                x = -(cols * self.pixel_spacing[1]) / 2.0
                y = -(rows * self.pixel_spacing[0]) / 2.0
                z = -(num_slices * self.slice_thickness) / 2.0
                base_position = (x, y, z)

            # Calculate position for each slice
            positions = []
            for i in range(num_slices):
                slice_z = base_position[2] + (i * self.slice_thickness)
                positions.append((base_position[0], base_position[1], slice_z))

            return positions

    @classmethod
    def from_array(
        cls,
        pixel_array: np.ndarray,
        pixel_spacing: Tuple[float, float],
        slice_thickness: float,
        patient_info: Optional[Dict[str, Union[str, int]]] = None,
        **kwargs,
    ) -> "CTSeries":
        """Create CT series from NumPy array.

        Primary factory method for creating CT DICOM files from image arrays.
        Handles both 2D single-slice and 3D multi-slice data with automatic
        geometric parameter calculation and clinical validation.

        Args:
            pixel_array: CT image data as NumPy array. Should contain calibrated
                values (Hounsfield Units preferred) with typical range -1000 to +3000.
            pixel_spacing: Physical pixel spacing in mm as (row_spacing, col_spacing).
                Must be positive values, typically 0.5-2.0mm for clinical CT.
            slice_thickness: Slice thickness in mm. Must be positive, typically
                1.0-5.0mm for RT planning applications.
            patient_info: Patient information dictionary including 'PatientID' (required)
                and optional demographic data following DICOM VR constraints.
            **kwargs: Additional parameters including image_position, image_orientation,
                patient_position, rescale parameters, and CT acquisition settings.

        Returns:
            CTSeries instance ready for validation and DICOM file creation.

        Raises:
            DicomCreationError: If array format is invalid, geometric parameters
                are inconsistent, or clinical validation fails.
            ValidationError: If patient information or clinical parameters are
                invalid or outside acceptable ranges.

        Clinical Notes:
            This method performs comprehensive validation to ensure the resulting
            DICOM files are clinically appropriate:

            **Image Quality**: Validates array dimensions and value ranges to ensure
            clinically meaningful image data.

            **Geometric Accuracy**: Checks pixel spacing and slice thickness against
            typical clinical ranges to prevent obvious errors in spatial calibration.

            **Patient Safety**: Validates patient information for completeness and
            DICOM compliance to ensure proper identification in clinical systems.

            **TPS Compatibility**: Ensures generated files conform to requirements
            of major treatment planning systems.

        Examples:
            Basic CT slice creation:

            >>> ct_data = np.random.randint(-1000, 3000, (512, 512), dtype=np.int16)
            >>> ct_series = CTSeries.from_array(
            ...     ct_data,
            ...     pixel_spacing=(1.0, 1.0),
            ...     slice_thickness=2.5,
            ...     patient_info={'PatientID': 'RT001'}
            ... )
            >>> ct_series.save('patient_ct.dcm')

            High-resolution planning CT with custom parameters:

            >>> hires_ct = np.random.randint(-1000, 3000, (1024, 1024), dtype=np.int16)
            >>> ct_series = CTSeries.from_array(
            ...     hires_ct,
            ...     pixel_spacing=(0.5, 0.5),
            ...     slice_thickness=1.25,
            ...     patient_info={
            ...         'PatientID': 'STEREO_001',
            ...         'PatientName': 'Doe^John',
            ...         'StudyDescription': 'Stereotactic Planning CT'
            ...     },
            ...     ct_parameters={'KVP': 120, 'XRayTubeCurrent': 300},
            ...     patient_position='HFS'
            ... )

            Multi-slice CT volume for comprehensive planning:

            >>> ct_volume = np.random.randint(-1000, 3000, (200, 512, 512), dtype=np.int16)  # (Z, Y, X)
            >>> ct_series = CTSeries.from_array(
            ...     ct_volume,
            ...     pixel_spacing=(1.0, 1.0),
            ...     slice_thickness=2.5,
            ...     patient_info={'PatientID': 'IMRT_001'},
            ...     image_position=(-256.0, -256.0, -250.0)  # Custom positioning
            ... )
            >>> file_paths = ct_series.save_series('patient_ct_series')
            >>> print(f"Created {len(file_paths)} CT slices")

            Research CT with anonymization:

            >>> research_ct = np.random.randint(-1000, 3000, (256, 256, 100), dtype=np.int16)
            >>> ct_series = CTSeries.from_array(
            ...     research_ct,
            ...     pixel_spacing=(2.0, 2.0),
            ...     slice_thickness=5.0,
            ...     patient_info={'PatientID': 'RESEARCH_001'}
            ... )
            >>> anon_paths = ct_series.save_series('research_ct', anonymize=True)
        """
        return cls(
            pixel_array=pixel_array,
            pixel_spacing=pixel_spacing,
            slice_thickness=slice_thickness,
            patient_info=patient_info,
            **kwargs,
        )

    def _create_modality_specific_dataset(self) -> Dataset:
        """Create CT-specific DICOM dataset.

        Returns:
            Complete DICOM dataset for CT image.

        Raises:
            DicomCreationError: If dataset creation fails.
        """
        if self._is_multi_slice:
            raise DicomCreationError(
                "Multi-slice datasets require save_series() method",
                suggestions=[
                    "Use save_series() to create multiple DICOM files for 3D volumes",
                    "For single slice, use 2D array input",
                    "Consider using save_single_slice() for individual slices",
                ],
            )

        # Create dataset using CT template
        dataset = CTImageTemplate.create_dataset(
            pixel_array=self.pixel_array,
            pixel_spacing=self.pixel_spacing,
            slice_thickness=self.slice_thickness,
            image_position=self.image_positions[0],
            image_orientation=self.image_orientation,
            rescale_intercept=self.rescale_intercept,
            rescale_slope=self.rescale_slope,
            PatientPosition=self.patient_position,
            **self.ct_parameters,
        )

        return dataset

    def _validate_modality_specific(self) -> None:
        """Perform CT-specific validation."""
        # Validate geometric parameters
        if len(self.pixel_spacing) != 2:
            self._validation_errors.append(
                f"Pixel spacing must have 2 elements, got {len(self.pixel_spacing)}"
            )
        elif any(spacing <= 0 for spacing in self.pixel_spacing):
            self._validation_errors.append("Pixel spacing values must be positive")
        elif any(spacing > 10.0 for spacing in self.pixel_spacing):
            self._validation_errors.append(
                "Pixel spacing values seem too large (>10mm). Typical range: 0.5-2.0mm"
            )

        if self.slice_thickness <= 0:
            self._validation_errors.append("Slice thickness must be positive")
        elif self.slice_thickness > 20.0:
            self._validation_errors.append(
                "Slice thickness seems too large (>20mm). Typical range: 1.0-5.0mm"
            )

        # Validate pixel array values for clinical reasonableness
        if self.pixel_array.size > 0:
            min_val, max_val = self.pixel_array.min(), self.pixel_array.max()

            # Convert to Hounsfield Units for validation
            min_hu = min_val * self.rescale_slope + self.rescale_intercept
            max_hu = max_val * self.rescale_slope + self.rescale_intercept

            # Use more reasonable validation range that accounts for rescale parameters
            # Typical CT range is -1000 to +3000 HU, but allow wider range for edge cases
            if min_hu < -3000 or max_hu > 6000:
                self._validation_errors.append(
                    f"Pixel values outside typical CT range: {min_hu:.0f} to {max_hu:.0f} HU. "
                    "Typical range: -1000 to +3000 HU"
                )

        # Validate patient position
        valid_positions = ["HFS", "HFP", "FFS", "FFP", "HFDR", "HFDL", "FFDR", "FFDL"]
        if self.patient_position not in valid_positions:
            self._validation_errors.append(
                f"Invalid patient position '{self.patient_position}'. "
                f"Valid options: {', '.join(valid_positions)}"
            )

    def save_series(
        self,
        base_filepath: Union[str, Path],
        validate: bool = True,
        anonymize: bool = False,
    ) -> List[Path]:
        """Save multi-slice CT series as individual DICOM files.

        Args:
            base_filepath: Base filename for series. Slice numbers will be appended.
                Example: 'ct_series' becomes 'ct_series_001.dcm', 'ct_series_002.dcm', etc.
            validate: Whether to validate each slice before saving.
            anonymize: Whether to anonymize patient information.

        Returns:
            List of Path objects for all created DICOM files.

        Raises:
            DicomCreationError: If series creation fails.
            ValidationError: If validation fails for any slice.

        Clinical Notes:
            Multi-slice series creation maintains proper DICOM relationships:
            - Consistent Study and Series UIDs across all slices
            - Proper Frame of Reference UID for spatial consistency
            - Sequential instance numbers and slice positions
            - Geometric consistency for dose calculation accuracy

        Examples:
            Save complete CT planning series:

            >>> ct_volume = np.random.randint(-1000, 3000, (200, 512, 512), dtype=np.int16)  # (Z, Y, X)
            >>> ct_series = CTSeries.from_array(ct_volume, (1.0, 1.0), 2.5)
            >>> file_paths = ct_series.save_series('planning_ct')
            >>> print(f"Created {len(file_paths)} files: {file_paths[0]} to {file_paths[-1]}")
        """
        base_path = Path(base_filepath)

        if not self._is_multi_slice:
            # Single slice - save as single file
            if base_path.is_dir():
                output_path = base_path / "ct_slice.dcm"
            else:
                output_path = base_path.with_suffix(".dcm")
            return [self.save(output_path, validate=validate, anonymize=anonymize)]

        # Multi-slice series (Z, Y, X) format
        file_paths = []
        num_slices, rows, cols = self.pixel_array.shape

        # Generate UIDs for consistent series
        study_uid = self.uid_generator.generate_study_instance_uid()
        series_uid = self.uid_generator.generate_series_instance_uid()
        frame_of_ref_uid = self.uid_generator.generate_frame_of_reference_uid()

        self.uid_registry.register_study_uid(study_uid)
        self.uid_registry.register_series_uid(series_uid, study_uid)
        self.uid_registry.register_frame_reference_uid(frame_of_ref_uid, study_uid)

        for i in range(num_slices):
            # Create individual slice dataset (Z, Y, X) format
            slice_array = self.pixel_array[i, :, :]
            slice_position = self.image_positions[i]

            # Generate unique instance UID for this slice
            instance_uid = self.uid_generator.generate_sop_instance_uid()
            self.uid_registry.register_instance_uid(instance_uid, series_uid)

            # Create dataset for this slice
            slice_dataset = CTImageTemplate.create_dataset(
                pixel_array=slice_array,
                pixel_spacing=self.pixel_spacing,
                slice_thickness=self.slice_thickness,
                image_position=slice_position,
                image_orientation=self.image_orientation,
                rescale_intercept=self.rescale_intercept,
                rescale_slope=self.rescale_slope,
                PatientPosition=self.patient_position,
                InstanceNumber=i + 1,
                **self.ct_parameters,
            )

            # Set consistent UIDs
            slice_dataset.StudyInstanceUID = study_uid
            slice_dataset.SeriesInstanceUID = series_uid
            slice_dataset.SOPInstanceUID = instance_uid
            slice_dataset.FrameOfReferenceUID = frame_of_ref_uid

            # Create temporary single-slice CTSeries for saving
            temp_ct = CTSeries(
                pixel_array=slice_array,
                pixel_spacing=self.pixel_spacing,
                slice_thickness=self.slice_thickness,
                image_position=slice_position,
                image_orientation=self.image_orientation,
                patient_position=self.patient_position,
                rescale_intercept=self.rescale_intercept,
                rescale_slope=self.rescale_slope,
                ct_parameters=self.ct_parameters,
                patient_info=self.patient_info,
                reference_image=self.reference_image,
                uid_generator=self.uid_generator,
            )

            # Override the dataset with our prepared one
            temp_ct.dataset = slice_dataset

            # Set patient and study info
            temp_ct._set_patient_info(slice_dataset, anonymize=anonymize)
            temp_ct._set_study_info(slice_dataset)

            # Generate filename with slice number
            if base_path.is_dir():
                # base_path is a directory - save files directly in that directory
                slice_filename = f"ct_slice_{i+1:03d}.dcm"
                slice_path = base_path / slice_filename
            else:
                # base_path is a filename prefix - use original behavior
                slice_filename = f"{base_path.stem}_{i+1:03d}.dcm"
                slice_path = base_path.parent / slice_filename

            # Save slice
            if validate:
                temp_ct.validate()

            saved_path = temp_ct.save(slice_path, validate=False, anonymize=False)
            file_paths.append(saved_path)

            # Update progress logging
            if (i + 1) % 50 == 0 or i == num_slices - 1:
                self.logger.info(
                    f"CT series progress: {i+1}/{num_slices} slices created",
                    extra={
                        "progress_percent": ((i + 1) / num_slices) * 100,
                        "slices_created": i + 1,
                        "total_slices": num_slices,
                    },
                )

        self.logger.info(
            f"CT series creation completed: {len(file_paths)} files",
            extra={
                "series_uid": series_uid,
                "study_uid": study_uid,
                "total_files": len(file_paths),
                "base_filename": str(base_path),
            },
        )

        return file_paths
