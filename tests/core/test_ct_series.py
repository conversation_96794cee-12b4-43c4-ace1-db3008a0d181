"""
Tests for CT Series Creation Implementation.

Comprehensive test suite for CTSeries class including array validation,
geometric handling, multi-slice series creation, and clinical parameter
validation according to the MVP roadmap requirements.
"""

import pytest
import numpy as np
from pathlib import Path
import tempfile
import shutil
from unittest.mock import Mock, patch
import pydicom

from pyrt_dicom.core.ct_series import CTSeries
from pyrt_dicom.utils.exceptions import DicomCreationError, ValidationError


class TestCTSeriesValidation:
    """Test validation and error handling in CTSeries."""

    def test_valid_2d_array_creation(self):
        """Test successful creation with valid 2D array."""
        pixel_array = np.random.randint(-1000, 3000, (512, 512), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={"PatientID": "TEST001"},
        )

        assert ct_series.pixel_array.shape == (512, 512)
        assert ct_series.pixel_spacing == (1.0, 1.0)
        assert ct_series.slice_thickness == 2.5
        assert not ct_series._is_multi_slice

    def test_valid_3d_array_creation(self):
        """Test successful creation with valid 3D array."""
        pixel_array = np.random.randint(-1000, 3000, (100, 256, 256), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={"PatientID": "TEST001"},
        )

        assert ct_series.pixel_array.shape == (100, 256, 256)
        assert ct_series._is_multi_slice
        assert len(ct_series.image_positions) == 100

    def test_invalid_array_type(self):
        """Test error with non-numpy array input."""
        with pytest.raises(DicomCreationError) as exc_info:
            CTSeries(
                pixel_array=[[1, 2], [3, 4]],  # Python list instead of numpy array
                pixel_spacing=(1.0, 1.0),
                slice_thickness=2.5,
            )
        assert "numpy.ndarray" in str(exc_info.value)

    def test_invalid_array_dimensions(self):
        """Test error with invalid array dimensions."""
        # Test 1D array
        with pytest.raises(DicomCreationError) as exc_info:
            CTSeries(
                pixel_array=np.array([1, 2, 3, 4]),
                pixel_spacing=(1.0, 1.0),
                slice_thickness=2.5,
            )
        assert "2D" in str(exc_info.value) and "3D" in str(exc_info.value)

        # Test 4D array
        with pytest.raises(DicomCreationError) as exc_info:
            CTSeries(
                pixel_array=np.random.rand(64, 64, 32, 2),
                pixel_spacing=(1.0, 1.0),
                slice_thickness=2.5,
            )
        assert "2D" in str(exc_info.value) and "3D" in str(exc_info.value)

    def test_too_small_image_dimensions(self):
        """Test error with clinically unrealistic small images."""
        with pytest.raises(DicomCreationError) as exc_info:
            CTSeries(
                pixel_array=np.random.rand(32, 32),  # Too small for clinical CT
                pixel_spacing=(1.0, 1.0),
                slice_thickness=2.5,
            )
        assert "64x64" in str(exc_info.value)
        assert "minimum" in str(exc_info.value).lower()

    def test_large_volume_warning(self, caplog):
        """Test warning for very large CT volumes."""
        # Create large volume that should trigger warning (don't mock the validation)
        # Use 1100 slices to trigger warning but avoid "suspiciously large" validation error
        large_array = np.random.randint(-1000, 3000, (1100, 128, 128), dtype=np.int16)

        # Create CT series - this should trigger the warning during validation
        ct_series = CTSeries(
            pixel_array=large_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=1.0,
            patient_info={"PatientID": "TEST001"},  # Add required patient info
        )

        # Warning should be logged for volumes > 1000 slices
        assert "Large CT volume detected" in caplog.text

    def test_pixel_spacing_validation(self):
        """Test pixel spacing parameter validation."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={"PatientID": "TEST001"},
        )

        # Test validation catches invalid pixel spacing
        ct_series.pixel_spacing = (0.0, 1.0)  # Zero spacing invalid

        with pytest.raises(ValidationError):
            ct_series.validate()

        # Test validation catches unrealistic pixel spacing
        ct_series.pixel_spacing = (15.0, 1.0)  # Too large

        with pytest.raises(ValidationError):
            ct_series.validate()

    def test_slice_thickness_validation(self):
        """Test slice thickness parameter validation."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={"PatientID": "TEST001"},
        )

        # Test validation catches invalid slice thickness
        ct_series.slice_thickness = 0.0  # Zero thickness invalid

        with pytest.raises(ValidationError):
            ct_series.validate()

        # Test validation catches unrealistic slice thickness
        ct_series.slice_thickness = 25.0  # Too large

        with pytest.raises(ValidationError):
            ct_series.validate()

    def test_patient_position_validation(self):
        """Test patient position validation."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_position="INVALID",  # Invalid position
            patient_info={"PatientID": "TEST001"},
        )

        with pytest.raises(ValidationError):
            ct_series.validate()

    def test_hounsfield_unit_range_validation(self):
        """Test validation of pixel values in Hounsfield Unit range."""
        # Create array with values outside typical CT range (but use valid dimensions)
        # Using 8000 as pixel value: 8000 * 1.0 + (-1024) = 6976 HU (exceeds 6000 HU limit)
        pixel_array = np.full((64, 64), 8000, dtype=np.int16)  # Use minimum valid size

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={"PatientID": "TEST001"},
        )

        with pytest.raises(ValidationError) as exc_info:
            ct_series.validate()
        assert "HU" in str(exc_info.value)
        assert "range" in str(exc_info.value).lower()


class TestCTSeriesFromArray:
    """Test the from_array class method factory."""

    def test_from_array_basic(self):
        """Test basic from_array creation."""
        pixel_array = np.random.randint(-1000, 3000, (256, 256), dtype=np.int16)

        ct_series = CTSeries.from_array(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={"PatientID": "FROM_ARRAY_001"},
        )

        assert isinstance(ct_series, CTSeries)
        assert ct_series.patient_info["PatientID"] == "FROM_ARRAY_001"
        assert ct_series.pixel_array.shape == (256, 256)

    def test_from_array_with_all_parameters(self):
        """Test from_array with comprehensive parameters."""
        pixel_array = np.random.randint(-1000, 3000, (512, 512), dtype=np.int16)

        patient_info = {
            "PatientID": "COMPREHENSIVE_001",
            "PatientName": "Test^Patient",
            "StudyDescription": "Planning CT",
        }

        ct_parameters = {
            "KVP": 120,
            "XRayTubeCurrent": 300,
            "ConvolutionKernel": "BONE",
        }

        ct_series = CTSeries.from_array(
            pixel_array=pixel_array,
            pixel_spacing=(0.75, 0.75),
            slice_thickness=1.25,
            patient_info=patient_info,
            image_position=(-192.0, -192.0, 100.0),
            patient_position="HFS",
            ct_parameters=ct_parameters,
        )

        assert ct_series.patient_info["PatientID"] == "COMPREHENSIVE_001"
        assert ct_series.pixel_spacing == (0.75, 0.75)
        assert ct_series.slice_thickness == 1.25
        assert ct_series.patient_position == "HFS"
        assert ct_series.ct_parameters["KVP"] == 120

    def test_from_array_multi_slice(self):
        """Test from_array with 3D multi-slice data."""
        pixel_array = np.random.randint(-1000, 3000, (50, 128, 128), dtype=np.int16)

        ct_series = CTSeries.from_array(
            pixel_array=pixel_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=5.0,
            patient_info={"PatientID": "MULTI_SLICE_001"},
        )

        assert ct_series._is_multi_slice
        assert len(ct_series.image_positions) == 50
        assert ct_series.pixel_array.shape == (50, 128, 128)


class TestCTSeriesGeometricHandling:
    """Test geometric parameter handling and image positioning."""

    def test_default_image_position_calculation_2d(self):
        """Test automatic image position calculation for 2D array."""
        pixel_array = np.random.randint(-1000, 3000, (256, 256), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array, pixel_spacing=(1.0, 1.0), slice_thickness=2.5
        )

        # Should center image at origin
        expected_x = -(256 * 1.0) / 2.0  # -128.0
        expected_y = -(256 * 1.0) / 2.0  # -128.0
        expected_z = 0.0

        assert len(ct_series.image_positions) == 1
        assert ct_series.image_positions[0] == (expected_x, expected_y, expected_z)

    def test_default_image_position_calculation_3d(self):
        """Test automatic image position calculation for 3D array."""
        pixel_array = np.random.randint(-1000, 3000, (20, 128, 128), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array, pixel_spacing=(2.0, 2.0), slice_thickness=5.0
        )

        # Should center volume
        expected_x = -(128 * 2.0) / 2.0  # -128.0
        expected_y = -(128 * 2.0) / 2.0  # -128.0
        expected_z = -(20 * 5.0) / 2.0  # -50.0

        assert len(ct_series.image_positions) == 20
        assert ct_series.image_positions[0] == (expected_x, expected_y, expected_z)

        # Check slice positions increment correctly
        for i in range(20):
            expected_slice_z = expected_z + (i * 5.0)
            assert ct_series.image_positions[i][2] == expected_slice_z

    def test_custom_image_position(self):
        """Test custom image position specification."""
        pixel_array = np.random.randint(-1000, 3000, (10, 64, 64), dtype=np.int16)
        custom_position = (-50.0, -75.0, 100.0)

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=3.0,
            image_position=custom_position,
        )

        assert ct_series.image_positions[0] == custom_position

        # Check that subsequent slices increment z correctly
        for i in range(10):
            expected_z = custom_position[2] + (i * 3.0)
            assert ct_series.image_positions[i] == (
                custom_position[0],
                custom_position[1],
                expected_z,
            )

    def test_coordinate_transformer_integration(self):
        """Test integration with coordinate transformer."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array, pixel_spacing=(1.0, 1.0), slice_thickness=2.5
        )

        # Should have coordinate transformer initialized
        assert hasattr(ct_series, "coordinate_transformer")
        assert ct_series.coordinate_transformer is not None


class TestCTSeriesDicomCreation:
    """Test DICOM dataset creation and file saving."""

    def test_single_slice_dataset_creation(self):
        """Test creation of DICOM dataset for single slice."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={"PatientID": "SINGLE_001"},
        )

        dataset = ct_series._create_modality_specific_dataset()

        assert hasattr(dataset, "SOPClassUID")
        assert dataset.Modality == "CT"
        assert dataset.Rows == 128
        assert dataset.Columns == 128

    def test_multi_slice_dataset_creation_error(self):
        """Test that multi-slice arrays require special handling."""
        pixel_array = np.random.randint(-1000, 3000, (10, 64, 64), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array, pixel_spacing=(1.0, 1.0), slice_thickness=2.5
        )

        # Should raise error when trying to create single dataset from multi-slice
        with pytest.raises(DicomCreationError) as exc_info:
            ct_series._create_modality_specific_dataset()
        assert "save_series" in str(exc_info.value)

    def test_ct_parameters_integration(self):
        """Test integration of CT acquisition parameters."""
        pixel_array = np.random.randint(-1000, 3000, (64, 64), dtype=np.int16)

        custom_params = {
            "KVP": 140,
            "XRayTubeCurrent": 400,
            "ConvolutionKernel": "LUNG",
        }

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            ct_parameters=custom_params,
        )

        assert ct_series.ct_parameters["KVP"] == 140
        assert ct_series.ct_parameters["XRayTubeCurrent"] == 400
        assert ct_series.ct_parameters["ConvolutionKernel"] == "LUNG"

        # Should also have defaults for unspecified parameters
        assert "ExposureTime" in ct_series.ct_parameters
        assert "FilterType" in ct_series.ct_parameters


class TestCTSeriesFileSaving:
    """Test file saving functionality for both single and multi-slice."""

    def setup_method(self):
        """Set up temporary directory for test files."""
        self.temp_dir = Path(tempfile.mkdtemp())

    def teardown_method(self):
        """Clean up temporary directory."""
        shutil.rmtree(self.temp_dir)

    def test_save_single_slice(self):
        """Test saving single CT slice to DICOM file."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={"PatientID": "SAVE_TEST_001"},
        )

        output_path = self.temp_dir / "single_slice.dcm"
        saved_path = ct_series.save(output_path)

        assert saved_path.exists()
        assert saved_path == output_path

        # Verify file can be read back
        dataset = pydicom.dcmread(saved_path)
        assert dataset.PatientID == "SAVE_TEST_001"
        assert dataset.Modality == "CT"
        assert dataset.Rows == 128
        assert dataset.Columns == 128

    def test_save_series_multi_slice(self):
        """Test saving multi-slice CT series."""
        pixel_array = np.random.randint(-1000, 3000, (5, 64, 64), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=5.0,
            patient_info={"PatientID": "MULTI_SAVE_001"},
        )

        base_path = self.temp_dir / "multi_slice_series"
        file_paths = ct_series.save_series(base_path)

        assert len(file_paths) == 5

        # Check that all files exist and are named correctly
        for i, file_path in enumerate(file_paths):
            assert file_path.exists()
            expected_name = f"multi_slice_series_{i+1:03d}.dcm"
            assert file_path.name == expected_name

            # Verify each file can be read
            dataset = pydicom.dcmread(file_path)
            assert dataset.PatientID == "MULTI_SAVE_001"
            assert dataset.Modality == "CT"
            assert dataset.InstanceNumber == i + 1

    def test_save_series_single_slice_fallback(self):
        """Test that save_series works with single slice (fallback behavior)."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={"PatientID": "FALLBACK_001"},
        )

        base_path = self.temp_dir / "single_fallback"
        file_paths = ct_series.save_series(base_path)

        assert len(file_paths) == 1
        assert file_paths[0].exists()
        assert file_paths[0].name == "single_fallback.dcm"

    def test_save_with_validation_disabled(self):
        """Test saving with validation disabled."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            # Deliberately omit PatientID to cause validation error
        )

        output_path = self.temp_dir / "no_validation.dcm"

        # Should succeed when validation is disabled
        saved_path = ct_series.save(output_path, validate=False)
        assert saved_path.exists()

    def test_save_with_anonymization(self):
        """Test saving with patient information anonymization."""
        pixel_array = np.random.randint(-1000, 3000, (128, 128), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={
                "PatientID": "ORIGINAL_ID",
                "PatientName": "Original^Patient^Name",
            },
        )

        output_path = self.temp_dir / "anonymized.dcm"
        saved_path = ct_series.save(output_path, anonymize=True)

        # Verify anonymization was applied
        dataset = pydicom.dcmread(saved_path)
        assert dataset.PatientID == "ANON"
        assert dataset.PatientName == "ANONYMOUS"

    def test_uid_consistency_in_series(self):
        """Test that UIDs are consistent across multi-slice series."""
        pixel_array = np.random.randint(-1000, 3000, (3, 64, 64), dtype=np.int16)

        ct_series = CTSeries(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={"PatientID": "UID_TEST_001"},
        )

        base_path = self.temp_dir / "uid_test_series"
        file_paths = ct_series.save_series(base_path)

        # Read all datasets and check UID consistency
        datasets = [pydicom.dcmread(path) for path in file_paths]

        # Study and Series UIDs should be consistent
        study_uid = datasets[0].StudyInstanceUID
        series_uid = datasets[0].SeriesInstanceUID
        frame_ref_uid = datasets[0].FrameOfReferenceUID

        for dataset in datasets[1:]:
            assert dataset.StudyInstanceUID == study_uid
            assert dataset.SeriesInstanceUID == series_uid
            assert dataset.FrameOfReferenceUID == frame_ref_uid

        # Instance UIDs should be unique
        instance_uids = [ds.SOPInstanceUID for ds in datasets]
        assert len(set(instance_uids)) == len(instance_uids)  # All unique

        # Instance numbers should be sequential
        instance_numbers = [ds.InstanceNumber for ds in datasets]
        assert instance_numbers == [1, 2, 3]


class TestCTSeriesPerformance:
    """Test performance requirements and benchmarks."""

    def test_large_single_slice_performance(self):
        """Test performance with large single slice (512x512)."""
        import time

        # Create large single slice
        pixel_array = np.random.randint(-1000, 3000, (512, 512), dtype=np.int16)

        start_time = time.time()
        ct_series = CTSeries.from_array(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={"PatientID": "PERF_001"},
        )
        creation_time = time.time() - start_time

        # Should create quickly (< 1 second for single slice)
        assert creation_time < 1.0

    def test_memory_efficiency_multi_slice(self):
        """Test memory efficiency with multi-slice data."""
        # Create moderately sized volume
        pixel_array = np.random.randint(-1000, 3000, (50, 256, 256), dtype=np.int16)

        ct_series = CTSeries.from_array(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={"PatientID": "MEMORY_001"},
        )

        # Should not duplicate pixel data unnecessarily
        assert ct_series.pixel_array is pixel_array or np.array_equal(
            ct_series.pixel_array, pixel_array
        )

    @pytest.mark.slow
    def test_target_performance_benchmark(self):
        """Test performance target: <5 seconds for 200-slice CT series."""
        import time

        # Create target-sized CT volume (200 slices)
        pixel_array = np.random.randint(-1000, 3000, (200, 512, 512), dtype=np.int16)  # (Z, Y, X)

        start_time = time.time()
        ct_series = CTSeries.from_array(
            pixel_array=pixel_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5,
            patient_info={"PatientID": "BENCHMARK_001"},
        )

        # Create temporary directory for saving
        with tempfile.TemporaryDirectory() as temp_dir:
            base_path = Path(temp_dir) / "benchmark_series"
            file_paths = ct_series.save_series(
                base_path, validate=False
            )  # Skip validation for pure creation speed

        total_time = time.time() - start_time

        # Should meet performance target: <5 seconds for 200 slices
        assert total_time < 5.0, f"Performance target not met: {total_time:.2f}s > 5.0s"
        assert len(file_paths) == 200
