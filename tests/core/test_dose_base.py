"""
Test suite for RT Dose Base Class Implementation.

Tests the RTDose class functionality including initialization, dose array
processing, geometric parameter extraction, validation, and integration
with the BaseDicomCreator framework.

## Test Coverage

- RTDose class instantiation and initialization
- from_array() factory method with various parameters
- Geometric parameter extraction from reference images
- Dose scaling calculation using PyMedPhys patterns
- Modality-specific validation logic  
- Integration with BaseDicomCreator framework
- Clinical validation and safety checks
- Error handling and exception scenarios

## Clinical Safety

All tests verify that RTDose objects maintain clinical accuracy and safety:
- Dose array validation and range checking
- Geometric consistency with reference CT images
- Proper scaling factor calculation for optimal precision
- DICOM compliance and multi-vendor compatibility
"""

import pytest
import numpy as np
import pydicom
from pydicom.dataset import Dataset
from pathlib import Path

from pyrt_dicom.core.rt_dose import RTDose
from pyrt_dicom.utils.exceptions import DicomCreationError, ValidationError


class TestRTDoseInitialization:
    """Test RTDose class initialization and basic functionality."""

    def test_basic_initialization(self):
        """Test basic RTDose initialization without parameters."""
        rt_dose = RTDose()
        
        # Verify inheritance from BaseDicomCreator
        assert hasattr(rt_dose, 'patient_info')
        assert hasattr(rt_dose, 'uid_generator')
        assert hasattr(rt_dose, 'uid_registry')
        
        # Verify RT Dose specific attributes
        assert rt_dose.dose_array is None
        assert rt_dose.dose_units == 'GY'
        assert rt_dose.dose_type == 'PHYSICAL'
        assert rt_dose.summation_type == 'PLAN'
        assert rt_dose.dose_scaling is None

    def test_initialization_with_patient_info(self):
        """Test initialization with patient information."""
        patient_info = {
            'PatientID': 'RT001',
            'PatientName': 'Doe^John',
            'StudyDescription': 'Prostate IMRT'
        }
        
        rt_dose = RTDose(patient_info=patient_info)
        
        assert rt_dose.patient_info == patient_info
        assert rt_dose.patient_info['PatientID'] == 'RT001'

    def test_initialization_with_reference_image_dataset(self):
        """Test initialization with reference image as Dataset."""
        # Create mock CT reference dataset
        ref_dataset = Dataset()
        ref_dataset.Modality = 'CT'
        ref_dataset.PixelSpacing = [2.0, 2.0]
        ref_dataset.SliceThickness = 2.5
        ref_dataset.ImagePositionPatient = [-100.0, -100.0, -50.0]
        ref_dataset.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ref_dataset.FrameOfReferenceUID = '*******.5.6.7'
        
        rt_dose = RTDose(reference_image=ref_dataset)
        
        assert rt_dose.reference_image == ref_dataset
        assert rt_dose.pixel_spacing == (2.0, 2.0)
        assert rt_dose.slice_thickness == 2.5
        assert rt_dose.image_position_patient == (-100.0, -100.0, -50.0)
        assert rt_dose.image_orientation_patient == (1, 0, 0, 0, 1, 0)

    def test_coordinate_transformer_initialization(self):
        """Test that coordinate transformer is properly initialized."""
        rt_dose = RTDose()
        
        assert hasattr(rt_dose, 'coordinate_transformer')
        assert rt_dose.coordinate_transformer is not None


class TestFromArrayFactoryMethod:
    """Test RTDose.from_array() factory method functionality."""

    def test_from_array_basic(self):
        """Test basic from_array creation with minimal parameters."""
        dose_array = np.random.rand(10, 64, 64) * 70.0
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.5,
            image_position_patient=(-64.0, -64.0, -12.5),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'  
        )
        
        assert np.array_equal(rt_dose.dose_array, dose_array)
        assert rt_dose.dose_units == 'GY'
        assert rt_dose.dose_type == 'PHYSICAL'
        assert rt_dose.summation_type == 'PLAN'
        assert rt_dose.dose_scaling is not None
        assert rt_dose.dose_scaling > 0

    def test_from_array_with_reference_image(self):
        """Test from_array with reference image for geometric parameters."""
        dose_array = np.random.rand(8, 48, 48) * 60.0
        
        # Create mock reference CT
        ref_dataset = Dataset()
        ref_dataset.Modality = 'CT'
        ref_dataset.PixelSpacing = [1.5, 1.5]
        ref_dataset.SliceThickness = 3.0
        ref_dataset.ImagePositionPatient = [-36.0, -36.0, -12.0]
        ref_dataset.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        ref_dataset.FrameOfReferenceUID = '*******.5.6.7'
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            reference_image=ref_dataset
        )
        
        assert np.array_equal(rt_dose.dose_array, dose_array)
        assert rt_dose.pixel_spacing == (1.5, 1.5)
        assert rt_dose.slice_thickness == 3.0
        assert rt_dose.image_position_patient == (-36.0, -36.0, -12.0)

    def test_from_array_with_custom_dose_parameters(self):
        """Test from_array with custom dose units, type, and summation."""
        dose_array = np.random.rand(6, 32, 32) * 100.0  # Relative dose 0-100%
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            dose_units='RELATIVE',
            dose_type='EFFECTIVE', 
            summation_type='BEAM',
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        assert rt_dose.dose_units == 'RELATIVE'
        assert rt_dose.dose_type == 'EFFECTIVE'
        assert rt_dose.summation_type == 'BEAM'

    def test_from_array_with_rt_plan_reference(self):
        """Test from_array with RT Plan reference."""
        dose_array = np.random.rand(5, 40, 40) * 50.0
        plan_sop_uid = "*******.5.6.7.8.9.10"
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            referenced_rt_plan_sop_instance_uid=plan_sop_uid,
            pixel_spacing=(2.5, 2.5),
            slice_thickness=2.5,
            image_position_patient=(-50.0, -50.0, -6.25),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        assert rt_dose.referenced_rt_plan_sop_instance_uid == plan_sop_uid

    def test_from_array_with_patient_info(self):
        """Test from_array with patient information."""
        dose_array = np.random.rand(4, 32, 32) * 40.0
        patient_info = {
            'PatientID': 'RT002',
            'PatientName': 'Smith^Jane',
            'StudyDescription': 'Breast RT'
        }
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            patient_info=patient_info,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=3.0,
            image_position_patient=(-32.0, -32.0, -6.0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        assert rt_dose.patient_info == patient_info


class TestDoseScalingCalculation:
    """Test dose scaling calculation algorithm."""

    def test_dose_scaling_calculation_basic(self):
        """Test basic dose scaling calculation."""
        dose_array = np.ones((4, 32, 32)) * 50.0  # Uniform 50 Gy dose
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Verify scaling is calculated
        assert rt_dose.dose_scaling is not None
        assert rt_dose.dose_scaling > 0
        
        # Verify scaling follows PyMedPhys pattern: max_dose / max_pixel_value
        max_dose = np.max(dose_array)
        max_pixel_value = np.iinfo(np.uint32).max
        expected_scaling = max_dose / max_pixel_value
        
        # Allow small epsilon difference due to overflow protection
        assert abs(rt_dose.dose_scaling - expected_scaling) < expected_scaling * 0.001

    def test_dose_scaling_with_zero_dose(self):
        """Test dose scaling calculation with array containing zeros."""
        dose_array = np.zeros((3, 16, 16))
        dose_array[1, 8, 8] = 30.0  # Single non-zero voxel
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Scaling should be based on maximum value (30.0)
        max_dose = 30.0
        max_pixel_value = np.iinfo(np.uint32).max
        expected_scaling = max_dose / max_pixel_value
        
        assert abs(rt_dose.dose_scaling - expected_scaling) < expected_scaling * 0.001

    def test_dose_scaling_precision_optimization(self):
        """Test that dose scaling optimizes for maximum precision."""
        # High dose case
        high_dose_array = np.random.rand(3, 16, 16) * 80.0
        rt_dose_high = RTDose.from_array(
            dose_array=high_dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Low dose case
        low_dose_array = np.random.rand(3, 16, 16) * 10.0
        rt_dose_low = RTDose.from_array(
            dose_array=low_dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # High dose should have larger scaling factor
        assert rt_dose_high.dose_scaling > rt_dose_low.dose_scaling
        
        # Both should use full dynamic range of pixel values
        max_pixel_high = np.max(high_dose_array) / rt_dose_high.dose_scaling
        max_pixel_low = np.max(low_dose_array) / rt_dose_low.dose_scaling
        
        max_uint32 = np.iinfo(np.uint32).max
        
        # Both should be close to maximum pixel value (within epsilon)
        assert max_pixel_high > max_uint32 * 0.999
        assert max_pixel_low > max_uint32 * 0.999


class TestGeometricParameterExtraction:
    """Test extraction of geometric parameters from reference images."""

    def test_extract_geometric_parameters_complete(self):
        """Test extraction when all geometric parameters are present."""
        ref_dataset = Dataset()
        ref_dataset.PixelSpacing = [1.25, 1.25]
        ref_dataset.SliceThickness = 1.5
        ref_dataset.ImagePositionPatient = [-75.0, -75.0, -30.0]
        ref_dataset.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        
        rt_dose = RTDose(reference_image=ref_dataset)
        
        assert rt_dose.pixel_spacing == (1.25, 1.25)
        assert rt_dose.slice_thickness == 1.5
        assert rt_dose.image_position_patient == (-75.0, -75.0, -30.0)
        assert rt_dose.image_orientation_patient == (1, 0, 0, 0, 1, 0)

    def test_extract_geometric_parameters_spacing_between_slices(self):
        """Test extraction using SpacingBetweenSlices when SliceThickness missing."""
        ref_dataset = Dataset()
        ref_dataset.PixelSpacing = [2.0, 2.0]
        ref_dataset.SpacingBetweenSlices = 3.0  # Use this instead of SliceThickness
        ref_dataset.ImagePositionPatient = [50.0, 50.0, 25.0]
        ref_dataset.ImageOrientationPatient = [1, 0, 0, 0, 1, 0]
        
        rt_dose = RTDose(reference_image=ref_dataset)
        
        assert rt_dose.slice_thickness == 3.0

    def test_extract_geometric_parameters_partial(self):
        """Test extraction when some parameters are missing."""
        ref_dataset = Dataset()
        ref_dataset.PixelSpacing = [2.0, 2.0]
        # Missing SliceThickness, ImagePositionPatient, ImageOrientationPatient
        
        rt_dose = RTDose(reference_image=ref_dataset)
        
        assert rt_dose.pixel_spacing == (2.0, 2.0)
        assert rt_dose.slice_thickness is None
        assert rt_dose.image_position_patient is None
        assert rt_dose.image_orientation_patient is None

    def test_extract_geometric_parameters_invalid_data(self):
        """Test handling of invalid geometric parameter data."""
        ref_dataset = Dataset()
        ref_dataset.PixelSpacing = [1.0, 2.0]
        # Create an incomplete ImagePositionPatient (missing z coordinate)
        ref_dataset.ImagePositionPatient = [0.0, 0.0]  # Missing 3rd element
        
        with pytest.raises(DicomCreationError) as exc_info:
            RTDose(reference_image=ref_dataset)
        
        assert "Failed to extract geometric parameters" in str(exc_info.value)

    def test_override_geometric_parameters(self):
        """Test that kwargs override reference image geometric parameters."""
        ref_dataset = Dataset()
        ref_dataset.PixelSpacing = [2.0, 2.0]
        ref_dataset.SliceThickness = 2.5
        
        dose_array = np.random.rand(4, 32, 32) * 40.0
        
        # Override with different values
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            reference_image=ref_dataset,
            pixel_spacing=(1.0, 1.0),  # Override reference value
            slice_thickness=1.5,       # Override reference value
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Should use override values, not reference image values
        assert rt_dose.pixel_spacing == (1.0, 1.0)
        assert rt_dose.slice_thickness == 1.5


class TestModalitySpecificValidation:
    """Test RT Dose specific validation logic."""

    def test_validation_requires_dose_array(self):
        """Test that validation fails when dose_array is missing."""
        rt_dose = RTDose()
        # Don't set dose_array
        
        with pytest.raises(ValidationError) as exc_info:
            rt_dose.validate()
        
        assert "RT Dose requires dose_array to be set" in str(exc_info.value)

    def test_validation_dose_scaling_positive(self):
        """Test validation of positive dose scaling."""
        dose_array = np.random.rand(3, 16, 16) * 30.0
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Manually set invalid scaling to test validation
        rt_dose.dose_scaling = -0.001
        
        with pytest.raises(ValidationError) as exc_info:
            rt_dose.validate()
        
        assert "DoseGridScaling must be positive" in str(exc_info.value)

    def test_validation_dose_scaling_overflow_protection(self):
        """Test validation detects scaling that would cause overflow."""
        dose_array = np.random.rand(3, 16, 16) * 1e10  # Extremely high dose
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Set scaling too small to cause overflow
        rt_dose.dose_scaling = 1e-6
        
        with pytest.raises(ValidationError) as exc_info:
            rt_dose.validate()
        
        assert "maximum pixel value" in str(exc_info.value)
        assert "exceeds uint32 limit" in str(exc_info.value)


class TestDatasetCreation:
    """Test DICOM dataset creation functionality."""

    def test_create_modality_specific_dataset_success(self):
        """Test successful dataset creation with all parameters."""
        dose_array = np.random.rand(5, 32, 32) * 50.0
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.5,
            image_position_patient=(-32.0, -32.0, -6.25),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        dataset = rt_dose._create_modality_specific_dataset()
        
        # Verify dataset is created
        assert dataset is not None
        assert hasattr(dataset, 'SOPClassUID')
        assert hasattr(dataset, 'Modality')
        assert dataset.Modality == 'RTDOSE'

    def test_create_dataset_missing_dose_array(self):
        """Test dataset creation fails when dose_array is missing."""
        rt_dose = RTDose()
        # Don't set dose_array
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_dose._create_modality_specific_dataset()
        
        assert "dose_array is required" in str(exc_info.value)

    def test_create_dataset_missing_geometric_parameters(self):
        """Test dataset creation fails when geometric parameters are missing."""
        dose_array = np.random.rand(3, 16, 16) * 30.0
        
        rt_dose = RTDose()
        rt_dose.dose_array = dose_array
        rt_dose.dose_scaling = 0.001
        # Don't set geometric parameters
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_dose._create_modality_specific_dataset()
        
        assert "missing geometric parameters" in str(exc_info.value)


class TestDoseStatistics:
    """Test dose statistics calculation functionality."""

    def test_get_dose_statistics_basic(self):
        """Test basic dose statistics calculation."""
        # Create dose array with known statistics
        dose_array = np.array([
            [[10.0, 20.0], [30.0, 40.0]],
            [[50.0, 60.0], [70.0, 80.0]]
        ])  # Shape: (2, 2, 2)
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        stats = rt_dose.get_dose_statistics()
        
        assert stats['min_dose'] == 10.0
        assert stats['max_dose'] == 80.0
        assert stats['mean_dose'] == 45.0  # (10+20+30+40+50+60+70+80)/8
        assert stats['total_voxels'] == 8
        assert stats['non_zero_voxels'] == 8  # All values non-zero
        assert 'dose_scaling' in stats
        assert 'max_pixel_value' in stats

    def test_get_dose_statistics_with_zeros(self):
        """Test dose statistics with zero dose regions."""
        dose_array = np.zeros((2, 3, 3))
        dose_array[0, 1, 1] = 50.0  # Single high dose voxel
        dose_array[1, 0, 0] = 25.0  # Another dose voxel
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        stats = rt_dose.get_dose_statistics()
        
        assert stats['min_dose'] == 0.0
        assert stats['max_dose'] == 50.0
        assert stats['total_voxels'] == 18  # 2*3*3
        assert stats['non_zero_voxels'] == 2  # Only 2 non-zero voxels
        
        # Verify percentiles (most values are zero, so median and 5th percentile will be 0)
        assert stats['dose_95'] > stats['median_dose']
        assert stats['dose_5'] <= stats['median_dose']  # Allow equal for mostly-zero arrays

    def test_get_dose_statistics_empty_array(self):
        """Test dose statistics when no dose array is set."""
        rt_dose = RTDose()
        # Don't set dose_array
        
        stats = rt_dose.get_dose_statistics()
        
        assert stats == {}  # Empty dictionary when no dose array


class TestStringRepresentation:
    """Test string representation functionality."""

    def test_repr_basic(self):
        """Test string representation with basic configuration."""
        rt_dose = RTDose(patient_info={'PatientID': 'RT003'})
        
        repr_str = repr(rt_dose)
        
        assert 'RTDose' in repr_str
        assert 'PatientID=RT003' in repr_str
        assert 'units=GY' in repr_str
        assert 'type=PHYSICAL' in repr_str
        assert 'summation=PLAN' in repr_str

    def test_repr_with_dose_array(self):
        """Test string representation with dose array configured."""
        dose_array = np.random.rand(4, 32, 32) * 40.0
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            dose_units='RELATIVE',
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        repr_str = repr(rt_dose)
        
        assert 'shape=(4, 32, 32)' in repr_str
        assert 'units=RELATIVE' in repr_str
        assert 'scaling=' in repr_str