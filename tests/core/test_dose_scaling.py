"""
Test suite for RT Dose Scaling Algorithm Implementation.

Tests the _calculate_dose_scaling method and related functionality for optimal
dose-to-pixel conversion following PyMedPhys patterns with precision optimization.

## Test Coverage

- Dose scaling calculation for various dose distributions
- Precision optimization validation 
- Edge case handling (zero dose, extreme values)
- Scaling factor accuracy comparison to PyMedPhys patterns
- Overflow protection validation
- Clinical range validation

## Clinical Safety

All tests verify that dose scaling maintains clinical accuracy:
- Sub-percent precision for dose values (<0.1% target)
- Maximum utilization of DICOM data type dynamic range
- Protection against numerical overflow and underflow
- Consistent scaling across different dose distributions
"""

import pytest
import numpy as np

from pyrt_dicom.core.rt_dose import RTDose
from pyrt_dicom.utils.exceptions import DicomCreationError


class TestDoseScalingBasic:
    """Test basic dose scaling calculation functionality."""

    def test_dose_scaling_uniform_dose(self):
        """Test dose scaling with uniform dose distribution."""
        dose_array = np.ones((4, 32, 32)) * 50.0  # Uniform 50 Gy dose
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Verify scaling is calculated
        assert rt_dose.dose_scaling is not None
        assert rt_dose.dose_scaling > 0
        
        # Verify scaling follows PyMedPhys pattern: max_dose / (max_pixel_value - epsilon)
        max_dose = np.max(dose_array)
        max_pixel_value = np.iinfo(np.uint32).max
        epsilon = 1e-6
        expected_scaling = max_dose / (max_pixel_value - epsilon)
        
        # Allow small difference due to epsilon protection
        assert abs(rt_dose.dose_scaling - expected_scaling) < expected_scaling * 0.01

    def test_dose_scaling_realistic_dose_distribution(self):
        """Test dose scaling with realistic clinical dose distribution."""
        # Create realistic dose distribution with dose gradient
        dose_array = np.zeros((8, 64, 64))
        
        # High dose region (target)
        dose_array[2:6, 20:44, 20:44] = 70.0  # Prescription dose
        
        # Medium dose region (transition)
        dose_array[1:7, 16:48, 16:48] = 35.0  # 50% isodose line
        
        # Low dose region (scattered dose)
        dose_array[:, :, :] += np.random.uniform(0, 5, dose_array.shape)  # Scattered dose
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.5,
            image_position_patient=(-64.0, -64.0, -10.0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Verify scaling optimizes for maximum dose
        max_dose = np.max(dose_array)
        max_pixel_value = np.iinfo(np.uint32).max
        
        # Calculated pixel value should be close to maximum (within 1%)
        calculated_max_pixel = max_dose / rt_dose.dose_scaling
        assert calculated_max_pixel > max_pixel_value * 0.99

    def test_dose_scaling_with_zero_regions(self):
        """Test dose scaling with array containing zero dose regions."""
        dose_array = np.zeros((6, 32, 32))
        
        # Add non-zero dose to specific regions
        dose_array[2, 15:17, 15:17] = 60.0  # High dose voxel
        dose_array[3, 10:20, 10:20] = 30.0  # Medium dose region
        dose_array[4, 5:25, 5:25] = 10.0   # Low dose region
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(1.5, 1.5),
            slice_thickness=3.0,
            image_position_patient=(-24.0, -24.0, -9.0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Scaling should be based on maximum value (60.0)
        max_dose = 60.0
        max_pixel_value = np.iinfo(np.uint32).max
        epsilon = 1e-6
        expected_scaling = max_dose / (max_pixel_value - epsilon)
        
        assert abs(rt_dose.dose_scaling - expected_scaling) < expected_scaling * 0.01


class TestDoseScalingPrecision:
    """Test dose scaling precision optimization and accuracy."""

    def test_dose_scaling_precision_different_dose_ranges(self):
        """Test scaling optimization for different dose ranges."""
        # High dose case (SBRT)
        high_dose_array = np.random.rand(5, 32, 32) * 80.0  # 0-80 Gy
        rt_dose_high = RTDose.from_array(
            dose_array=high_dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Low dose case (pediatric)
        low_dose_array = np.random.rand(5, 32, 32) * 15.0  # 0-15 Gy
        rt_dose_low = RTDose.from_array(
            dose_array=low_dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # High dose should have larger scaling factor
        assert rt_dose_high.dose_scaling > rt_dose_low.dose_scaling
        
        # Both should optimize pixel value usage
        max_pixel_uint32 = np.iinfo(np.uint32).max
        
        max_pixel_high = np.max(high_dose_array) / rt_dose_high.dose_scaling
        max_pixel_low = np.max(low_dose_array) / rt_dose_low.dose_scaling
        
        # Both should be close to maximum pixel value (within 1%)
        assert max_pixel_high > max_pixel_uint32 * 0.99
        assert max_pixel_low > max_pixel_uint32 * 0.99

    def test_dose_scaling_precision_validation(self):
        """Test that scaling achieves target precision (<0.1%)."""
        dose_array = np.random.rand(4, 24, 24) * 45.0  # 0-45 Gy
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.5, 2.5),
            slice_thickness=2.5,
            image_position_patient=(-30.0, -30.0, -5.0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Convert back from pixel values to dose and check precision
        pixel_values = (dose_array / rt_dose.dose_scaling).astype(np.uint32)
        reconstructed_dose = pixel_values * rt_dose.dose_scaling
        
        # Calculate relative error
        non_zero_mask = dose_array > 0
        relative_error = np.abs(reconstructed_dose - dose_array) / dose_array
        max_relative_error = np.max(relative_error[non_zero_mask])
        
        # Should achieve <0.1% precision target
        assert max_relative_error < 0.001  # 0.1%

    def test_dose_scaling_dynamic_range_utilization(self):
        """Test optimal utilization of 32-bit dynamic range."""
        dose_values = [1.0, 10.0, 50.0, 100.0, 200.0]  # Different max doses
        
        for max_dose in dose_values:
            dose_array = np.random.rand(3, 16, 16) * max_dose
            
            rt_dose = RTDose.from_array(
                dose_array=dose_array,
                pixel_spacing=(2.0, 2.0),
                slice_thickness=2.0,
                image_position_patient=(0, 0, 0),
                image_orientation_patient=(1, 0, 0, 0, 1, 0),
                frame_of_reference_uid='*******.5.6.7'
            )
            
            # Check that scaling utilizes most of the dynamic range
            max_pixel_value = max_dose / rt_dose.dose_scaling
            max_uint32 = np.iinfo(np.uint32).max
            
            utilization = max_pixel_value / max_uint32
            
            # Should utilize >99% of dynamic range
            assert utilization > 0.99


class TestDoseScalingEdgeCases:
    """Test dose scaling edge cases and error handling."""

    def test_dose_scaling_very_small_doses(self):
        """Test scaling with very small dose values."""
        # Micro-dose for radiobiology research
        dose_array = np.random.rand(3, 16, 16) * 0.001  # 0-1 mGy
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=1.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Should still calculate valid scaling
        assert rt_dose.dose_scaling > 0
        assert rt_dose.dose_scaling < 1e-6  # Very small scaling for micro-doses

    def test_dose_scaling_very_large_doses(self):
        """Test scaling with very large dose values (research/QA)."""
        # High dose for research/QA purposes
        dose_array = np.random.rand(3, 16, 16) * 1000.0  # 0-1000 Gy
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=1.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Should calculate valid scaling without overflow
        assert rt_dose.dose_scaling > 0
        
        # Verify no overflow occurs
        max_pixel_value = np.max(dose_array) / rt_dose.dose_scaling
        assert max_pixel_value <= np.iinfo(np.uint32).max

    def test_dose_scaling_single_voxel_dose(self):
        """Test scaling with single non-zero dose voxel."""
        dose_array = np.zeros((2, 8, 8))
        dose_array[1, 4, 4] = 25.0  # Single dose voxel
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Scaling should be based on the single maximum value
        max_dose = 25.0
        max_pixel_value = np.iinfo(np.uint32).max
        epsilon = 1e-6
        expected_scaling = max_dose / (max_pixel_value - epsilon)
        
        assert abs(rt_dose.dose_scaling - expected_scaling) < expected_scaling * 0.01


class TestDoseScalingValidation:
    """Test dose scaling validation and error conditions."""

    def test_dose_scaling_calculation_method_direct(self):
        """Test _calculate_dose_scaling method directly."""
        dose_array = np.random.rand(3, 20, 20) * 42.0
        
        rt_dose = RTDose()
        scaling = rt_dose._calculate_dose_scaling(dose_array)
        
        # Verify scaling calculation
        assert scaling > 0
        
        max_dose = np.max(dose_array)
        max_pixel_value = np.iinfo(np.uint32).max
        epsilon = 1e-6
        expected_scaling = max_dose / (max_pixel_value - epsilon)
        
        assert abs(scaling - expected_scaling) < expected_scaling * 0.01

    def test_dose_scaling_logging_verification(self):
        """Test that dose scaling calculation includes proper logging."""
        dose_array = np.random.rand(4, 16, 16) * 35.0
        
        # Create RTDose and check scaling is logged
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        # Verify scaling was calculated and logged
        assert rt_dose.dose_scaling is not None
        assert hasattr(rt_dose, 'logger')

    def test_dose_scaling_statistics_integration(self):
        """Test integration of dose scaling with dose statistics."""
        dose_array = np.random.rand(3, 12, 12) * 30.0
        
        rt_dose = RTDose.from_array(
            dose_array=dose_array,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7'
        )
        
        stats = rt_dose.get_dose_statistics()
        
        # Verify dose scaling is included in statistics
        assert 'dose_scaling' in stats
        assert stats['dose_scaling'] == rt_dose.dose_scaling
        assert 'max_pixel_value' in stats
        
        # Verify max pixel value calculation
        expected_max_pixel = stats['max_dose'] / stats['dose_scaling']
        assert abs(stats['max_pixel_value'] - expected_max_pixel) < 1.0