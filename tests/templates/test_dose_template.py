"""
Test suite for RT Dose IOD Template Implementation.

Tests DICOM compliance, parameter validation, and clinical safety for the
RTDoseTemplate class, ensuring generated DICOM datasets conform to the
RT Dose IOD specification (DICOM Part 3 C.8.8.3).

## Test Coverage

- DICOM IOD compliance validation
- Dose parameter validation (units, types, summation)
- Geometric parameter validation (spacing, orientation)
- Multi-frame pixel data structure
- PyMedPhys pattern integration
- Clinical range validation
- Error handling and messaging

## Clinical Safety

All tests verify that generated RT Dose objects maintain clinical accuracy
and safety requirements:
- Sub-percent dose precision through proper scaling
- Geometric consistency with planning CT
- Multi-vendor TPS compatibility
- Regulatory compliance for dose reporting
"""

import pytest
import numpy as np
import pydicom
from pydicom.dataset import Dataset

from pyrt_dicom.templates.dose_template import RTDoseTemplate
from pyrt_dicom.utils.exceptions import TemplateError


class TestRTDoseTemplate:
    """Test suite for RT Dose template functionality."""

    def test_sop_class_uid(self):
        """Test that RT Dose template uses correct SOP Class UID."""
        expected_uid = "1.2.840.10008.*******.1.481.2"  # RT Dose Storage
        assert RTDoseTemplate.SOP_CLASS_UID == expected_uid

    def test_required_modules_list(self):
        """Test that all required DICOM modules are specified."""
        expected_modules = [
            "Patient",
            "General Study",
            "RT Series",
            "Frame of Reference", 
            "General Equipment",
            "General Image",
            "Image Plane",
            "Image Pixel",
            "Multi-frame",
            "RT Dose",
            "SOP Common",
        ]
        assert RTDoseTemplate.REQUIRED_MODULES == expected_modules

    def test_dose_units_validation(self):
        """Test dose units validation constants."""
        expected_units = ['GY', 'RELATIVE']
        assert RTDoseTemplate.DOSE_UNITS == expected_units

    def test_dose_types_validation(self):
        """Test dose types validation constants."""
        expected_types = ['PHYSICAL', 'EFFECTIVE', 'ERROR']
        assert RTDoseTemplate.DOSE_TYPES == expected_types

    def test_summation_types_validation(self):
        """Test summation types validation constants."""
        expected_summations = ['PLAN', 'BEAM', 'BRACHY', 'CONTROL_POINT']
        assert RTDoseTemplate.SUMMATION_TYPES == expected_summations

    def test_create_basic_dataset(self):
        """Test creation of basic RT Dose dataset with minimal parameters."""
        # Create test dose array (Z, Y, X) convention
        dose_array = np.random.rand(10, 64, 64) * 70.0  # 70 Gy max dose
        dose_scaling = 70.0 / np.iinfo(np.uint32).max

        dataset = RTDoseTemplate.create_dataset(
            dose_array=dose_array,
            dose_scaling=dose_scaling,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.5,
            image_position_patient=(-64.0, -64.0, -12.5),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7',
        )

        # Verify basic dataset structure
        assert dataset.SOPClassUID == RTDoseTemplate.SOP_CLASS_UID
        assert hasattr(dataset, 'SOPInstanceUID')
        assert dataset.Modality == "RTDOSE"
        assert dataset.DoseUnits == 'GY'
        assert dataset.DoseType == 'PHYSICAL'
        assert dataset.DoseSummationType == 'PLAN'

    def test_dataset_geometric_parameters(self):
        """Test proper setting of geometric parameters in dataset."""
        dose_array = np.random.rand(5, 32, 32) * 50.0
        dose_scaling = 50.0 / np.iinfo(np.uint32).max
        pixel_spacing = (1.5, 1.5)
        slice_thickness = 3.0
        image_position = (-48.0, -48.0, -7.5)
        image_orientation = (1, 0, 0, 0, 1, 0)

        dataset = RTDoseTemplate.create_dataset(
            dose_array=dose_array,
            dose_scaling=dose_scaling,
            pixel_spacing=pixel_spacing,
            slice_thickness=slice_thickness,
            image_position_patient=image_position,
            image_orientation_patient=image_orientation,
            frame_of_reference_uid='*******.5.6.7',
        )

        # Verify geometric parameters
        assert dataset.PixelSpacing == [1.5, 1.5]
        assert dataset.SliceThickness == 3.0
        assert dataset.ImagePositionPatient == [-48.0, -48.0, -7.5]
        assert dataset.ImageOrientationPatient == [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]

    def test_dataset_multiframe_structure(self):
        """Test proper multi-frame DICOM structure creation."""
        num_slices = 8
        dose_array = np.random.rand(num_slices, 48, 48) * 60.0
        dose_scaling = 60.0 / np.iinfo(np.uint32).max
        slice_thickness = 2.0
        z_start = -8.0

        dataset = RTDoseTemplate.create_dataset(
            dose_array=dose_array,
            dose_scaling=dose_scaling,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=slice_thickness,
            image_position_patient=(-48.0, -48.0, z_start),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7',
        )

        # Verify multi-frame structure
        assert dataset.NumberOfFrames == num_slices
        assert dataset.FrameIncrementPointer == 0x3004000C
        assert len(dataset.GridFrameOffsetVector) == num_slices
        
        # Verify z-positions are correct
        expected_z_positions = [z_start + i * slice_thickness for i in range(num_slices)]
        np.testing.assert_array_almost_equal(
            dataset.GridFrameOffsetVector, expected_z_positions
        )

    def test_dataset_dose_parameters(self):
        """Test setting of dose-specific parameters."""
        dose_array = np.random.rand(6, 40, 40) * 80.0
        dose_scaling = 0.002  # Custom scaling factor

        dataset = RTDoseTemplate.create_dataset(
            dose_array=dose_array,
            dose_scaling=dose_scaling,
            pixel_spacing=(2.5, 2.5),
            slice_thickness=2.5,
            image_position_patient=(-50.0, -50.0, -7.5),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7',
            dose_units='GY',
            dose_type='EFFECTIVE',
            summation_type='BEAM',
        )

        # Verify dose parameters
        assert dataset.DoseUnits == 'GY'
        assert dataset.DoseType == 'EFFECTIVE'
        assert dataset.DoseSummationType == 'BEAM'
        assert dataset.DoseGridScaling == 0.002

    def test_dataset_with_rt_plan_reference(self):
        """Test dataset creation with RT Plan reference."""
        dose_array = np.random.rand(4, 32, 32) * 40.0
        dose_scaling = 40.0 / np.iinfo(np.uint32).max
        plan_sop_uid = "*******.5.6.8.9.10"

        dataset = RTDoseTemplate.create_dataset(
            dose_array=dose_array,
            dose_scaling=dose_scaling,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=3.0,
            image_position_patient=(-32.0, -32.0, -6.0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7',
            referenced_rt_plan_sop_instance_uid=plan_sop_uid,
        )

        # Verify RT Plan reference
        assert hasattr(dataset, 'ReferencedRTPlanSequence')
        assert len(dataset.ReferencedRTPlanSequence) == 1
        
        ref_plan = dataset.ReferencedRTPlanSequence[0]
        assert ref_plan.ReferencedSOPClassUID == "1.2.840.10008.*******.1.481.5"
        assert ref_plan.ReferencedSOPInstanceUID == plan_sop_uid

    def test_invalid_dose_array_type(self):
        """Test validation of dose array type."""
        invalid_dose = [[1, 2], [3, 4]]  # List instead of numpy array

        with pytest.raises(TemplateError) as exc_info:
            RTDoseTemplate.create_dataset(
                dose_array=invalid_dose,
                dose_scaling=0.001,
                pixel_spacing=(2.0, 2.0),
                slice_thickness=2.0,
                image_position_patient=(0, 0, 0),
                image_orientation_patient=(1, 0, 0, 0, 1, 0),
                frame_of_reference_uid='*******.5.6.7',
            )

        assert "dose_array must be a numpy ndarray" in str(exc_info.value)
        assert "Convert dose data to numpy.ndarray format" in str(exc_info.value)

    def test_invalid_dose_array_dimensions(self):
        """Test validation of dose array dimensions."""
        # 2D array instead of 3D
        invalid_dose = np.random.rand(64, 64) * 50.0

        with pytest.raises(TemplateError) as exc_info:
            RTDoseTemplate.create_dataset(
                dose_array=invalid_dose,
                dose_scaling=0.001,
                pixel_spacing=(2.0, 2.0),
                slice_thickness=2.0,
                image_position_patient=(0, 0, 0),
                image_orientation_patient=(1, 0, 0, 0, 1, 0),
                frame_of_reference_uid='*******.5.6.7',
            )

        assert "dose_array must be 3D, got 2D array" in str(exc_info.value)
        assert "(slices, rows, columns)" in str(exc_info.value)

    def test_dose_array_with_nan_values(self):
        """Test validation rejects dose arrays with NaN values."""
        dose_array = np.random.rand(4, 32, 32) * 50.0
        dose_array[1, 10, 15] = np.nan  # Inject NaN value

        with pytest.raises(TemplateError) as exc_info:
            RTDoseTemplate.create_dataset(
                dose_array=dose_array,
                dose_scaling=0.001,
                pixel_spacing=(2.0, 2.0),
                slice_thickness=2.0,
                image_position_patient=(0, 0, 0),
                image_orientation_patient=(1, 0, 0, 0, 1, 0),
                frame_of_reference_uid='*******.5.6.7',
            )

        assert "dose_array contains non-finite values" in str(exc_info.value)
        assert "Check dose calculation for numerical errors" in str(exc_info.value)

    def test_invalid_dose_units(self):
        """Test validation of dose units parameter."""
        dose_array = np.random.rand(4, 32, 32) * 50.0

        with pytest.raises(TemplateError) as exc_info:
            RTDoseTemplate.create_dataset(
                dose_array=dose_array,
                dose_scaling=0.001,
                pixel_spacing=(2.0, 2.0),
                slice_thickness=2.0,
                image_position_patient=(0, 0, 0),
                image_orientation_patient=(1, 0, 0, 0, 1, 0),
                frame_of_reference_uid='*******.5.6.7',
                dose_units='INVALID',
            )

        assert "Invalid dose_units: 'INVALID'" in str(exc_info.value)
        assert "Use 'GY' for absolute dose in Gray" in str(exc_info.value)

    def test_invalid_dose_type(self):
        """Test validation of dose type parameter."""
        dose_array = np.random.rand(4, 32, 32) * 50.0

        with pytest.raises(TemplateError) as exc_info:
            RTDoseTemplate.create_dataset(
                dose_array=dose_array,
                dose_scaling=0.001,
                pixel_spacing=(2.0, 2.0),
                slice_thickness=2.0,
                image_position_patient=(0, 0, 0),
                image_orientation_patient=(1, 0, 0, 0, 1, 0),
                frame_of_reference_uid='*******.5.6.7',
                dose_type='INVALID',
            )

        assert "Invalid dose_type: 'INVALID'" in str(exc_info.value)
        assert "Use 'PHYSICAL' for standard treatment planning dose" in str(exc_info.value)

    def test_invalid_summation_type(self):
        """Test validation of summation type parameter."""
        dose_array = np.random.rand(4, 32, 32) * 50.0

        with pytest.raises(TemplateError) as exc_info:
            RTDoseTemplate.create_dataset(
                dose_array=dose_array,
                dose_scaling=0.001,
                pixel_spacing=(2.0, 2.0),
                slice_thickness=2.0,
                image_position_patient=(0, 0, 0),
                image_orientation_patient=(1, 0, 0, 0, 1, 0),
                frame_of_reference_uid='*******.5.6.7',
                summation_type='INVALID',
            )

        assert "Invalid summation_type: 'INVALID'" in str(exc_info.value)
        assert "Use 'PLAN' for total plan dose distribution" in str(exc_info.value)

    def test_invalid_pixel_spacing_length(self):
        """Test validation of pixel spacing parameter length."""
        dose_array = np.random.rand(4, 32, 32) * 50.0

        with pytest.raises(TemplateError) as exc_info:
            RTDoseTemplate.create_dataset(
                dose_array=dose_array,
                dose_scaling=0.001,
                pixel_spacing=(2.0, 2.0, 2.0),  # Too many elements
                slice_thickness=2.0,
                image_position_patient=(0, 0, 0),
                image_orientation_patient=(1, 0, 0, 0, 1, 0),
                frame_of_reference_uid='*******.5.6.7',
            )

        assert "pixel_spacing must have 2 elements" in str(exc_info.value)

    def test_negative_pixel_spacing(self):
        """Test validation rejects negative pixel spacing."""
        dose_array = np.random.rand(4, 32, 32) * 50.0

        with pytest.raises(TemplateError) as exc_info:
            RTDoseTemplate.create_dataset(
                dose_array=dose_array,
                dose_scaling=0.001,
                pixel_spacing=(-2.0, 2.0),  # Negative row spacing
                slice_thickness=2.0,
                image_position_patient=(0, 0, 0),
                image_orientation_patient=(1, 0, 0, 0, 1, 0),
                frame_of_reference_uid='*******.5.6.7',
            )

        assert "pixel_spacing[0] must be positive" in str(exc_info.value)

    def test_negative_slice_thickness(self):
        """Test validation rejects negative slice thickness."""
        dose_array = np.random.rand(4, 32, 32) * 50.0

        with pytest.raises(TemplateError) as exc_info:
            RTDoseTemplate.create_dataset(
                dose_array=dose_array,
                dose_scaling=0.001,
                pixel_spacing=(2.0, 2.0),
                slice_thickness=-2.0,  # Negative slice thickness
                image_position_patient=(0, 0, 0),
                image_orientation_patient=(1, 0, 0, 0, 1, 0),
                frame_of_reference_uid='*******.5.6.7',
            )

        assert "slice_thickness must be positive" in str(exc_info.value)

    def test_invalid_image_position_length(self):
        """Test validation of image position parameter length."""
        dose_array = np.random.rand(4, 32, 32) * 50.0

        with pytest.raises(TemplateError) as exc_info:
            RTDoseTemplate.create_dataset(
                dose_array=dose_array,
                dose_scaling=0.001,
                pixel_spacing=(2.0, 2.0),
                slice_thickness=2.0,
                image_position_patient=(0, 0),  # Missing z coordinate
                image_orientation_patient=(1, 0, 0, 0, 1, 0),
                frame_of_reference_uid='*******.5.6.7',
            )

        assert "image_position_patient must have 3 elements" in str(exc_info.value)

    def test_invalid_image_orientation_length(self):
        """Test validation of image orientation parameter length."""
        dose_array = np.random.rand(4, 32, 32) * 50.0

        with pytest.raises(TemplateError) as exc_info:
            RTDoseTemplate.create_dataset(
                dose_array=dose_array,
                dose_scaling=0.001,
                pixel_spacing=(2.0, 2.0),
                slice_thickness=2.0,
                image_position_patient=(0, 0, 0),
                image_orientation_patient=(1, 0, 0, 0, 1),  # Missing element
                frame_of_reference_uid='*******.5.6.7',
            )

        assert "image_orientation_patient must have 6 elements" in str(exc_info.value)

    def test_non_unit_orientation_vectors(self):
        """Test validation of orientation vector normalization."""
        dose_array = np.random.rand(4, 32, 32) * 50.0

        with pytest.raises(TemplateError) as exc_info:
            RTDoseTemplate.create_dataset(
                dose_array=dose_array,
                dose_scaling=0.001,
                pixel_spacing=(2.0, 2.0),
                slice_thickness=2.0,
                image_position_patient=(0, 0, 0),
                image_orientation_patient=(2, 0, 0, 0, 2, 0),  # Not normalized
                frame_of_reference_uid='*******.5.6.7',
            )

        assert "Row direction cosines not normalized" in str(exc_info.value)

    def test_non_orthogonal_orientation_vectors(self):
        """Test validation of orientation vector orthogonality."""
        dose_array = np.random.rand(4, 32, 32) * 50.0

        with pytest.raises(TemplateError) as exc_info:
            RTDoseTemplate.create_dataset(
                dose_array=dose_array,
                dose_scaling=0.001,
                pixel_spacing=(2.0, 2.0),
                slice_thickness=2.0,
                image_position_patient=(0, 0, 0),
                image_orientation_patient=(1, 0, 0, 1, 0, 0),  # Not orthogonal
                frame_of_reference_uid='*******.5.6.7',
            )

        assert "Row and column direction cosines not orthogonal" in str(exc_info.value)

    def test_pixel_data_creation(self):
        """Test pixel data creation and conversion."""
        # Create test dose array with known values
        dose_array = np.ones((2, 4, 4)) * 10.0  # 10 Gy uniform dose
        dose_scaling = 0.001  # 1 mGy per pixel value

        pixel_data = RTDoseTemplate._create_pixel_data(dose_array, dose_scaling)

        # Verify pixel data is bytes
        assert isinstance(pixel_data, bytes)
        
        # Convert back to array to verify scaling
        pixel_array_flat = np.frombuffer(pixel_data, dtype=np.uint32)
        expected_pixel_value = int(10.0 / 0.001)  # 10000 
        
        # All values should be the expected pixel value
        assert np.all(pixel_array_flat == expected_pixel_value)

    def test_compliance_validation_valid_dataset(self):
        """Test compliance validation with valid dataset."""
        dose_array = np.random.rand(4, 32, 32) * 50.0
        dose_scaling = 0.001

        dataset = RTDoseTemplate.create_dataset(
            dose_array=dose_array,
            dose_scaling=dose_scaling,
            pixel_spacing=(2.0, 2.0),
            slice_thickness=2.0,
            image_position_patient=(0, 0, 0),
            image_orientation_patient=(1, 0, 0, 0, 1, 0),
            frame_of_reference_uid='*******.5.6.7',
        )

        errors = RTDoseTemplate.validate_compliance(dataset)
        assert len(errors) == 0  # Should be fully compliant

    def test_compliance_validation_missing_elements(self):
        """Test compliance validation detects missing required elements."""
        # Create minimal dataset missing required elements
        dataset = Dataset()
        dataset.SOPClassUID = RTDoseTemplate.SOP_CLASS_UID

        errors = RTDoseTemplate.validate_compliance(dataset)
        
        # Should detect multiple missing elements
        assert len(errors) > 5
        assert any("Missing required element: Modality" in error for error in errors)
        assert any("Missing required element: DoseUnits" in error for error in errors)
        assert any("Missing required element: NumberOfFrames" in error for error in errors)

    def test_compliance_validation_invalid_sop_class(self):
        """Test compliance validation detects invalid SOP Class UID."""
        dataset = Dataset()
        dataset.SOPClassUID = "*******.*******.9"  # Invalid SOP Class

        errors = RTDoseTemplate.validate_compliance(dataset)
        
        assert any("Invalid SOPClassUID" in error for error in errors)

    def test_compliance_validation_invalid_modality(self):
        """Test compliance validation detects invalid modality."""
        dataset = Dataset()
        dataset.SOPClassUID = RTDoseTemplate.SOP_CLASS_UID
        dataset.Modality = "CT"  # Wrong modality

        errors = RTDoseTemplate.validate_compliance(dataset)
        
        assert any("Invalid modality for RT Dose: CT" in error for error in errors)

    def test_compliance_validation_frame_count_mismatch(self):
        """Test compliance validation detects frame count inconsistencies."""
        dataset = Dataset()
        dataset.SOPClassUID = RTDoseTemplate.SOP_CLASS_UID
        dataset.Modality = "RTDOSE"
        dataset.NumberOfFrames = 5
        dataset.GridFrameOffsetVector = [0, 2, 4]  # Only 3 offsets for 5 frames

        errors = RTDoseTemplate.validate_compliance(dataset)
        
        assert any("NumberOfFrames (5) does not match GridFrameOffsetVector length (3)" in error for error in errors)

    def test_compliance_validation_invalid_dose_parameters(self):
        """Test compliance validation detects invalid dose parameters."""
        dataset = Dataset()
        dataset.SOPClassUID = RTDoseTemplate.SOP_CLASS_UID
        dataset.Modality = "RTDOSE"
        dataset.DoseUnits = "INVALID"
        dataset.DoseType = "INVALID"
        dataset.DoseSummationType = "INVALID"
        dataset.DoseGridScaling = -0.001  # Negative scaling

        errors = RTDoseTemplate.validate_compliance(dataset)
        
        assert any("Invalid DoseUnits: INVALID" in error for error in errors)
        assert any("Invalid DoseType: INVALID" in error for error in errors)
        assert any("Invalid DoseSummationType: INVALID" in error for error in errors)
        assert any("DoseGridScaling must be positive" in error for error in errors)