"""
Tests for RT Structure Set base class implementation.

Tests RTStructureSet class creation, structure management, and integration
with the base DICOM creator framework.
"""

import pytest
import numpy as np
import pydicom
from pydicom.dataset import Dataset
from pathlib import Path

from pyrt_dicom.core.rt_struct import RTStructureSet
from pyrt_dicom.utils.exceptions import Dicom<PERSON>reationError, ValidationError
from pyrt_dicom.templates.struct_template import RTStructureSetTemplate


class TestRTStructureSet:
    """Test RT Structure Set creation and management."""

    @pytest.fixture
    def sample_ct_dataset(self):
        """Create sample CT dataset for testing."""
        ct = Dataset()
        # Required DICOM attributes
        ct.SOPClassUID = "1.2.840.10008.5.1.4.1.1.2"  # CT Image Storage
        ct.SOPInstanceUID = "1.2.3.4.5.6.7.8"
        ct.Modality = "CT"
        ct.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        ct.ImagePositionPatient = [-250.0, -250.0, 100.0]
        ct.PixelSpacing = [1.0, 1.0]
        ct.SliceThickness = 2.5
        ct.FrameOfReferenceUID = "1.2.3.4.5.6.7"
        ct.PatientID = "TEST001"
        ct.PatientName = "Test^Patient"
        ct.StudyInstanceUID = "1.2.3.4.5.6"
        ct.SeriesInstanceUID = "1.2.3.4.5.6.7"
        
        # Add required image attributes
        ct.Rows = 512
        ct.Columns = 512
        ct.BitsAllocated = 16
        ct.BitsStored = 16
        ct.HighBit = 15
        ct.PixelRepresentation = 1  # Signed
        ct.RescaleIntercept = -1024.0
        ct.RescaleSlope = 1.0
        ct.PhotometricInterpretation = "MONOCHROME2"
        ct.SamplesPerPixel = 1
        ct.PlanarConfiguration = 0
        
        return ct

    @pytest.fixture
    def sample_masks(self):
        """Create sample binary masks for testing."""
        # Create 3D binary masks (small for testing) - (Z, Y, X) convention
        ptv_mask = np.zeros((32, 64, 64), dtype=bool)
        ptv_mask[10:22, 20:44, 20:44] = True  # Centered cube
        
        bladder_mask = np.zeros((32, 64, 64), dtype=bool)
        bladder_mask[8:16, 15:35, 30:50] = True  # Overlapping region
        
        rectum_mask = np.zeros((32, 64, 64), dtype=bool)
        rectum_mask[12:20, 30:50, 20:40] = True  # Adjacent region
        
        return {
            'PTV_7000': ptv_mask,
            'Bladder': bladder_mask,
            'Rectum': rectum_mask
        }

    @pytest.fixture
    def sample_patient_info(self):
        """Create sample patient information."""
        return {
            'PatientID': 'RT001',
            'PatientName': 'Doe^John^A',
            'PatientBirthDate': '19800101',
            'PatientSex': 'M',
            'StudyDescription': 'Prostate IMRT Planning'
        }

    def test_init_basic(self):
        """Test basic RTStructureSet initialization."""
        rt_struct = RTStructureSet()
        
        assert rt_struct.structures == {}
        assert rt_struct.structure_counter == 0
        assert rt_struct.coordinate_transformer is None
        assert rt_struct.frame_of_reference is None

    def test_init_with_reference_image(self, sample_ct_dataset):
        """Test RTStructureSet initialization with reference image dataset."""
        rt_struct = RTStructureSet(reference_image=sample_ct_dataset)
        
        assert rt_struct.reference_image == sample_ct_dataset
        assert rt_struct.coordinate_transformer is not None
        assert rt_struct.frame_of_reference is not None
        assert hasattr(rt_struct, '_frame_of_reference_uid')
        assert rt_struct._frame_of_reference_uid == sample_ct_dataset.FrameOfReferenceUID

    def test_init_with_patient_info(self, sample_patient_info):
        """Test RTStructureSet initialization with patient information."""
        rt_struct = RTStructureSet(patient_info=sample_patient_info)
        
        assert rt_struct.patient_info == sample_patient_info
        assert rt_struct.patient_info['PatientID'] == 'RT001'

    def test_from_masks_basic(self, sample_ct_dataset, sample_masks):
        """Test basic structure creation from masks."""
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=sample_masks
        )
        
        # Test structure creation
        assert len(rt_struct.structures) == 3
        assert 'PTV_7000' in rt_struct.structures
        assert 'Bladder' in rt_struct.structures
        assert 'Rectum' in rt_struct.structures
        
        # Test ROI number assignment
        roi_numbers = [struct['number'] for struct in rt_struct.structures.values()]
        assert sorted(roi_numbers) == [1, 2, 3]
        assert len(set(roi_numbers)) == 3  # All unique

    def test_from_masks_with_colors(self, sample_ct_dataset, sample_masks):
        """Test structure creation with explicit colors."""
        colors = [(255, 0, 0), (0, 0, 255), (0, 255, 0)]
        
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=sample_masks,
            colors=colors
        )
        
        # Test color assignment
        structure_names = ['PTV_7000', 'Bladder', 'Rectum']
        for i, name in enumerate(structure_names):
            assert rt_struct.structures[name]['color'] == colors[i]

    def test_from_masks_with_color_names(self, sample_ct_dataset, sample_masks):
        """Test structure creation with color names."""
        colors = ['red', 'blue', 'green']
        
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=sample_masks,
            colors=colors
        )
        
        # Test color name conversion
        expected_colors = [(255, 0, 0), (0, 0, 255), (0, 255, 0)]
        structure_names = ['PTV_7000', 'Bladder', 'Rectum']
        for i, name in enumerate(structure_names):
            assert rt_struct.structures[name]['color'] == expected_colors[i]

    def test_from_masks_with_structure_types(self, sample_ct_dataset, sample_masks):
        """Test structure creation with custom structure types."""
        structure_types = {
            'PTV_7000': 'PTV',
            'Bladder': 'ORGAN',
            'Rectum': 'ORGAN'
        }
        
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=sample_masks,
            structure_types=structure_types
        )
        
        # Test structure type assignment
        for name, expected_type in structure_types.items():
            assert rt_struct.structures[name]['type'] == expected_type

    def test_from_masks_with_algorithms(self, sample_ct_dataset, sample_masks):
        """Test structure creation with custom algorithms."""
        algorithms = {
            'PTV_7000': 'MANUAL',
            'Bladder': 'AUTOMATIC',
            'Rectum': 'SEMIAUTOMATIC'
        }
        
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=sample_masks,
            algorithms=algorithms
        )
        
        # Test algorithm assignment
        for name, expected_alg in algorithms.items():
            assert rt_struct.structures[name]['algorithm'] == expected_alg

    def test_from_masks_automatic_color_assignment(self, sample_ct_dataset):
        """Test automatic color assignment for clinical structure names."""
        masks = {
            'PTV': np.ones((32, 64, 64), dtype=bool),
            'BLADDER': np.ones((32, 64, 64), dtype=bool),
            'RECTUM': np.ones((32, 64, 64), dtype=bool)
        }
        
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=masks
        )
        
        # Test clinical colors are assigned
        assert rt_struct.structures['PTV']['color'] == RTStructureSetTemplate.CLINICAL_COLORS['PTV']
        assert rt_struct.structures['BLADDER']['color'] == RTStructureSetTemplate.CLINICAL_COLORS['Bladder']
        assert rt_struct.structures['RECTUM']['color'] == RTStructureSetTemplate.CLINICAL_COLORS['Rectum']

    def test_from_masks_automatic_structure_types(self, sample_ct_dataset):
        """Test automatic structure type assignment based on names."""
        masks = {
            'PTV_7000': np.ones((32, 64, 64), dtype=bool),
            'CTV_High': np.ones((32, 64, 64), dtype=bool),
            'GTV_Primary': np.ones((32, 64, 64), dtype=bool),
            'Bladder': np.ones((32, 64, 64), dtype=bool)
        }
        
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=masks
        )
        
        # Test automatic type assignment
        assert rt_struct.structures['PTV_7000']['type'] == 'PTV'
        assert rt_struct.structures['CTV_High']['type'] == 'CTV'
        assert rt_struct.structures['GTV_Primary']['type'] == 'GTV'
        assert rt_struct.structures['Bladder']['type'] == 'ORGAN'

    def test_from_masks_empty_masks_error(self, sample_ct_dataset):
        """Test error handling for empty masks dictionary."""
        with pytest.raises(DicomCreationError) as exc_info:
            RTStructureSet.from_masks(
                ct_reference=sample_ct_dataset,
                masks={}
            )
        
        error = exc_info.value
        assert "At least one structure mask must be provided" in str(error)
        assert error.clinical_context['provided_masks'] == 0

    def test_from_masks_names_masks_mismatch_error(self, sample_ct_dataset, sample_masks):
        """Test error handling for names/masks count mismatch."""
        names = ['Structure1']  # Only one name for three masks
        
        with pytest.raises(DicomCreationError) as exc_info:
            RTStructureSet.from_masks(
                ct_reference=sample_ct_dataset,
                masks=sample_masks,
                names=names
            )
        
        error = exc_info.value
        assert "Number of names" in str(error) and "doesn't match number of masks" in str(error)

    def test_add_structure_basic(self, sample_ct_dataset):
        """Test adding individual structure to structure set."""
        rt_struct = RTStructureSet(reference_image=sample_ct_dataset)
        
        mask = np.ones((32, 64, 64), dtype=bool)
        rt_struct.add_structure(
            mask=mask,
            name='Heart',
            color=(255, 0, 128),
            structure_type='ORGAN'
        )
        
        # Test structure was added
        assert 'Heart' in rt_struct.structures
        assert rt_struct.structures['Heart']['number'] == 1
        assert rt_struct.structures['Heart']['color'] == (255, 0, 128)
        assert rt_struct.structures['Heart']['type'] == 'ORGAN'

    def test_add_structure_automatic_color(self, sample_ct_dataset):
        """Test adding structure with automatic color assignment."""
        rt_struct = RTStructureSet(reference_image=sample_ct_dataset)
        
        mask = np.ones((32, 64, 64), dtype=bool)
        rt_struct.add_structure(
            mask=mask,
            name='PTV_Boost'
        )
        
        # Test clinical color was automatically assigned
        expected_color = RTStructureSetTemplate.get_clinical_color('PTV_Boost')
        assert rt_struct.structures['PTV_Boost']['color'] == expected_color

    def test_add_structure_color_name_conversion(self, sample_ct_dataset):
        """Test adding structure with color name conversion."""
        rt_struct = RTStructureSet(reference_image=sample_ct_dataset)
        
        mask = np.ones((32, 64, 64), dtype=bool)
        rt_struct.add_structure(
            mask=mask,
            name='Heart',
            color='pink'
        )
        
        # Test color name was converted to RGB
        assert rt_struct.structures['Heart']['color'] == (255, 192, 203)

    def test_add_structure_duplicate_name_error(self, sample_ct_dataset, sample_masks):
        """Test error handling for duplicate structure names."""
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=sample_masks
        )
        
        mask = np.ones((32, 64, 64), dtype=bool)
        with pytest.raises(DicomCreationError) as exc_info:
            rt_struct.add_structure(
                mask=mask,
                name='PTV_7000'  # Already exists
            )
        
        error = exc_info.value
        assert "already exists in structure set" in str(error)
        assert error.clinical_context['duplicate_name'] == 'PTV_7000'

    def test_get_structure_names(self, sample_ct_dataset, sample_masks):
        """Test getting list of structure names."""
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=sample_masks
        )
        
        names = rt_struct.get_structure_names()
        
        # Should be sorted by ROI number
        assert len(names) == 3
        assert all(name in ['PTV_7000', 'Bladder', 'Rectum'] for name in names)

    def test_get_structure_count(self, sample_ct_dataset, sample_masks):
        """Test getting structure count."""
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=sample_masks
        )
        
        assert rt_struct.get_structure_count() == 3

    def test_get_structure_info(self, sample_ct_dataset, sample_masks):
        """Test getting detailed structure information."""
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=sample_masks
        )
        
        info = rt_struct.get_structure_info('PTV_7000')
        
        assert info['name'] == 'PTV_7000'
        assert isinstance(info['number'], int)
        assert info['number'] >= 1
        assert isinstance(info['color'], tuple)
        assert len(info['color']) == 3
        assert info['type'] == 'PTV'  # Should be auto-detected
        assert info['algorithm'] == 'MANUAL'  # Default

    def test_get_structure_info_not_found_error(self, sample_ct_dataset, sample_masks):
        """Test error handling for structure info lookup with invalid name."""
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=sample_masks
        )
        
        with pytest.raises(KeyError) as exc_info:
            rt_struct.get_structure_info('NonExistent')
        
        assert "Structure 'NonExistent' not found" in str(exc_info.value)

    def test_validation_no_structures_error(self, sample_ct_dataset):
        """Test validation error when no structures are defined."""
        rt_struct = RTStructureSet(reference_image=sample_ct_dataset)
        
        with pytest.raises(ValidationError) as exc_info:
            rt_struct.validate()
        
        error = exc_info.value
        assert "No structures defined" in str(error)

    def test_validation_invalid_structure_name(self, sample_ct_dataset):
        """Test validation error for invalid structure name."""
        rt_struct = RTStructureSet(reference_image=sample_ct_dataset)
        
        # Add structure with invalid name
        rt_struct.structures['test'] = {
            'name': '',  # Invalid empty name
            'number': 1,
            'color': (255, 0, 0),
            'type': 'ORGAN',
            'algorithm': 'MANUAL'
        }
        
        with pytest.raises(ValidationError) as exc_info:
            rt_struct.validate()
        
        error = exc_info.value
        assert "Invalid structure name" in str(error)

    def test_validation_invalid_color(self, sample_ct_dataset):
        """Test validation error for invalid color values."""
        rt_struct = RTStructureSet(reference_image=sample_ct_dataset)
        
        # Add structure with invalid color
        rt_struct.structures['test'] = {
            'name': 'Test',
            'number': 1,
            'color': (300, -50, 128),  # Invalid RGB values
            'type': 'ORGAN',
            'algorithm': 'MANUAL'
        }
        
        with pytest.raises(ValidationError) as exc_info:
            rt_struct.validate()
        
        error = exc_info.value
        assert "Invalid RGB component" in str(error)

    def test_validation_invalid_structure_type(self, sample_ct_dataset):
        """Test validation error for invalid structure type."""
        rt_struct = RTStructureSet(reference_image=sample_ct_dataset)
        
        # Add structure with invalid type
        rt_struct.structures['test'] = {
            'name': 'Test',
            'number': 1,
            'color': (255, 0, 0),
            'type': 'INVALID_TYPE',  # Invalid RT ROI Interpreted Type
            'algorithm': 'MANUAL'
        }
        
        with pytest.raises(ValidationError) as exc_info:
            rt_struct.validate()
        
        error = exc_info.value
        assert "Invalid RT ROI Interpreted Type" in str(error)

    def test_validation_invalid_algorithm(self, sample_ct_dataset):
        """Test validation error for invalid algorithm."""
        rt_struct = RTStructureSet(reference_image=sample_ct_dataset)
        
        # Add structure with invalid algorithm
        rt_struct.structures['test'] = {
            'name': 'Test',
            'number': 1,
            'color': (255, 0, 0),
            'type': 'ORGAN',
            'algorithm': 'INVALID_ALG'  # Invalid algorithm
        }
        
        with pytest.raises(ValidationError) as exc_info:
            rt_struct.validate()
        
        error = exc_info.value
        assert "Invalid ROI Generation Algorithm" in str(error)

    def test_validation_invalid_mask(self, sample_ct_dataset):
        """Test validation error for invalid mask data."""
        rt_struct = RTStructureSet(reference_image=sample_ct_dataset)
        
        # Add structure with invalid mask
        rt_struct.structures['test'] = {
            'name': 'Test',
            'number': 1,
            'color': (255, 0, 0),
            'type': 'ORGAN',
            'algorithm': 'MANUAL',
            'mask': [[1, 0], [0, 1]]  # Not a numpy array
        }
        
        with pytest.raises(ValidationError) as exc_info:
            rt_struct.validate()
        
        error = exc_info.value
        assert "mask must be numpy array" in str(error)

    def test_create_dataset_basic(self, sample_ct_dataset, sample_masks):
        """Test basic DICOM dataset creation."""
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=sample_masks
        )
        
        dataset = rt_struct._create_modality_specific_dataset()
        
        # Test basic DICOM elements
        assert dataset.SOPClassUID == "1.2.840.10008.5.1.4.1.1.481.3"
        assert dataset.Modality == "RTSTRUCT"
        assert hasattr(dataset, 'StructureSetROISequence')
        assert len(dataset.StructureSetROISequence) == 3

    def test_create_dataset_no_structures_error(self, sample_ct_dataset):
        """Test error handling when creating dataset with no structures."""
        rt_struct = RTStructureSet(reference_image=sample_ct_dataset)
        
        with pytest.raises(DicomCreationError) as exc_info:
            rt_struct._create_modality_specific_dataset()
        
        error = exc_info.value
        assert "No structures defined" in str(error)

    def test_repr_string(self, sample_ct_dataset, sample_masks, sample_patient_info):
        """Test string representation for debugging."""
        rt_struct = RTStructureSet.from_masks(
            ct_reference=sample_ct_dataset,
            masks=sample_masks,
            patient_info=sample_patient_info
        )
        
        repr_str = repr(rt_struct)
        
        assert "RTStructureSet" in repr_str
        assert "PatientID=RT001" in repr_str
        assert "3 structures" in repr_str
        assert "with reference" in repr_str
        assert "not validated" in repr_str