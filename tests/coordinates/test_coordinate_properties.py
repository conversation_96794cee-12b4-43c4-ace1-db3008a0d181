"""
Property-based testing for coordinate transformation invariants.

This module uses hypothesis to test mathematical invariants and properties
of the coordinate transformation system that must hold for all valid inputs.
Tests include round-trip accuracy, geometric consistency, and boundary
condition behavior across random valid coordinate ranges.
"""

import numpy as np
import pytest
from hypothesis import given, strategies as st, assume, settings
from numpy.typing import NDArray

from pyrt_dicom.coordinates.transforms import (
    CoordinateTransformer,
    dicom_to_patient_coordinates,
    patient_to_dicom_coordinates,
    transform_image_orientation,
    IMAGE_ORIENTATION_MAP,
    DICOM_PATIENT_ORIENTATIONS,
)
from pyrt_dicom.utils.exceptions import CoordinateSystemError


# Valid coordinate ranges for clinical applications
MIN_COORDINATE = -2000.0  # mm
MAX_COORDINATE = 2000.0   # mm
MIN_PIXEL_SPACING = 0.1   # mm
MAX_PIXEL_SPACING = 5.0   # mm
MIN_SLICE_THICKNESS = 0.5 # mm
MAX_SLICE_THICKNESS = 10.0 # mm


@st.composite
def valid_pixel_spacing(draw):
    """Generate valid pixel spacing tuples."""
    row_spacing = draw(st.floats(min_value=MIN_PIXEL_SPACING, max_value=MAX_PIXEL_SPACING))
    col_spacing = draw(st.floats(min_value=MIN_PIXEL_SPACING, max_value=MAX_PIXEL_SPACING))
    return (row_spacing, col_spacing)


@st.composite
def valid_image_position(draw):
    """Generate valid image position coordinates."""
    x = draw(st.floats(min_value=MIN_COORDINATE, max_value=MAX_COORDINATE))
    y = draw(st.floats(min_value=MIN_COORDINATE, max_value=MAX_COORDINATE))
    z = draw(st.floats(min_value=MIN_COORDINATE, max_value=MAX_COORDINATE))
    return (x, y, z)


@st.composite
def valid_coordinates(draw):
    """Generate valid coordinate arrays for transformation."""
    num_points = draw(st.integers(min_value=1, max_value=10))
    coordinates = []
    for _ in range(num_points):
        x = draw(st.floats(min_value=-1000.0, max_value=1000.0))
        y = draw(st.floats(min_value=-1000.0, max_value=1000.0))
        z = draw(st.floats(min_value=-1000.0, max_value=1000.0))
        coordinates.append([x, y, z])
    return np.array(coordinates)


@st.composite
def valid_normalized_vector(draw):
    """Generate valid normalized direction vectors."""
    # Generate random vector
    x = draw(st.one_of(
        st.floats(min_value=-1.0, max_value=-0.01),
        st.floats(min_value=0.01, max_value=1.0),
        st.just(0.0)  # Allow zero for some components
    ))
    y = draw(st.one_of(
        st.floats(min_value=-1.0, max_value=-0.01),
        st.floats(min_value=0.01, max_value=1.0),
        st.just(0.0)  # Allow zero for some components
    ))
    z = draw(st.one_of(
        st.floats(min_value=-1.0, max_value=-0.01),
        st.floats(min_value=0.01, max_value=1.0),
        st.just(0.0)  # Allow zero for some components
    ))
    # Ensure not zero vector
    assume(x != 0 or y != 0 or z != 0)
    
    # Normalize
    vector = np.array([x, y, z])
    normalized = vector / np.linalg.norm(vector)
    return normalized.tolist()


@st.composite
def valid_orthogonal_orientation(draw):
    """Generate valid orthogonal image orientation vectors."""
    # Get first vector
    row_vector = np.array(draw(valid_normalized_vector()))
    
    # Generate second vector orthogonal to first
    # Create a random vector
    temp = draw(st.lists(st.floats(min_value=-1.0, max_value=1.0), min_size=3, max_size=3))
    temp_vector = np.array(temp)
    
    # Project out the component parallel to row_vector
    col_vector = temp_vector - np.dot(temp_vector, row_vector) * row_vector
    
    # Skip if vectors are parallel (would result in zero vector)
    assume(np.linalg.norm(col_vector) > 1e-6)
    
    # Normalize
    col_vector = col_vector / np.linalg.norm(col_vector)
    
    # Verify orthogonality and normalization
    assert abs(np.dot(row_vector, col_vector)) < 1e-10
    assert abs(np.linalg.norm(row_vector) - 1.0) < 1e-10
    assert abs(np.linalg.norm(col_vector) - 1.0) < 1e-10
    
    return row_vector.tolist() + col_vector.tolist()


class TestCoordinateTransformationProperties:
    """Property-based tests for coordinate transformation invariants."""

    @given(
        patient_position=st.sampled_from(list(IMAGE_ORIENTATION_MAP.keys())),
        pixel_spacing=valid_pixel_spacing(),
        slice_thickness=st.floats(min_value=MIN_SLICE_THICKNESS, max_value=MAX_SLICE_THICKNESS),
        image_position=valid_image_position(),
        coordinates=valid_coordinates(),
    )
    @settings(deadline=5000)  # 5 second deadline for complex tests
    def test_dicom_patient_round_trip_accuracy(
        self, patient_position, pixel_spacing, slice_thickness, image_position, coordinates
    ):
        """
        Test round-trip transformation accuracy: DICOM → Patient → DICOM.
        
        Property: For any valid coordinate, transforming from DICOM to patient
        coordinates and back to DICOM should return the original coordinates
        within numerical precision.
        """
        transformer = CoordinateTransformer(
            patient_position=patient_position,
            pixel_spacing=pixel_spacing,
            slice_thickness=slice_thickness,
            image_position=image_position,
        )
        
        # Forward transformation: DICOM → Patient
        patient_coords = transformer.dicom_to_patient(coordinates)
        
        # Reverse transformation: Patient → DICOM
        recovered_dicom_coords = transformer.patient_to_dicom(patient_coords)
        
        # Verify round-trip accuracy within numerical tolerance
        np.testing.assert_allclose(
            coordinates, recovered_dicom_coords,
            rtol=1e-10, atol=1e-10,
            err_msg="Round-trip transformation should preserve coordinates within numerical precision"
        )

    @given(
        patient_position=st.sampled_from(list(IMAGE_ORIENTATION_MAP.keys())),
        pixel_spacing=valid_pixel_spacing(),
        slice_thickness=st.floats(min_value=MIN_SLICE_THICKNESS, max_value=MAX_SLICE_THICKNESS),
        image_position=valid_image_position(),
        coordinates=valid_coordinates(),
    )
    @settings(deadline=5000)
    def test_patient_dicom_round_trip_accuracy(
        self, patient_position, pixel_spacing, slice_thickness, image_position, coordinates
    ):
        """
        Test round-trip transformation accuracy: Patient → DICOM → Patient.
        
        Property: For any valid coordinate, transforming from patient to DICOM
        coordinates and back to patient should return the original coordinates
        within numerical precision.
        """
        transformer = CoordinateTransformer(
            patient_position=patient_position,
            pixel_spacing=pixel_spacing,
            slice_thickness=slice_thickness,
            image_position=image_position,
        )
        
        # Forward transformation: Patient → DICOM
        dicom_coords = transformer.patient_to_dicom(coordinates)
        
        # Reverse transformation: DICOM → Patient
        recovered_patient_coords = transformer.dicom_to_patient(dicom_coords)
        
        # Verify round-trip accuracy within numerical tolerance
        np.testing.assert_allclose(
            coordinates, recovered_patient_coords,
            rtol=1e-10, atol=1e-10,
            err_msg="Round-trip transformation should preserve coordinates within numerical precision"
        )

    @given(
        patient_position=st.sampled_from(list(IMAGE_ORIENTATION_MAP.keys())),
        pixel_spacing=valid_pixel_spacing(),
        slice_thickness=st.floats(min_value=MIN_SLICE_THICKNESS, max_value=MAX_SLICE_THICKNESS),
        image_position=valid_image_position(),
    )
    @settings(deadline=5000)
    def test_transformation_matrix_invertibility(
        self, patient_position, pixel_spacing, slice_thickness, image_position
    ):
        """
        Test transformation matrix invertibility property.
        
        Property: The transformation matrix should be invertible and its inverse
        should produce the identity when multiplied with the original matrix.
        """
        transformer = CoordinateTransformer(
            patient_position=patient_position,
            pixel_spacing=pixel_spacing,
            slice_thickness=slice_thickness,
            image_position=image_position,
        )
        
        # Get transformation matrix
        transform_matrix = transformer.get_transformation_matrix()
        
        # Verify it's invertible (determinant should not be zero)
        det = np.linalg.det(transform_matrix[:3, :3])  # 3x3 rotation part
        assert abs(det) > 1e-10, "Transformation matrix should be invertible"
        
        # Compute inverse
        try:
            inverse_matrix = np.linalg.inv(transform_matrix)
            
            # Verify that matrix * inverse = identity
            identity_check = np.matmul(transform_matrix, inverse_matrix)
            expected_identity = np.eye(4)
            
            np.testing.assert_allclose(
                identity_check, expected_identity,
                rtol=1e-10, atol=1e-10,
                err_msg="Transformation matrix times its inverse should equal identity"
            )
        except np.linalg.LinAlgError:
            pytest.fail("Transformation matrix should be invertible")

    @given(
        patient_position=st.sampled_from(list(IMAGE_ORIENTATION_MAP.keys())),
        pixel_spacing=valid_pixel_spacing(),
        slice_thickness=st.floats(min_value=MIN_SLICE_THICKNESS, max_value=MAX_SLICE_THICKNESS),
        image_position=valid_image_position(),
        coordinates=valid_coordinates(),
        scale_factor=st.floats(min_value=0.1, max_value=10.0),
    )
    @settings(deadline=5000)
    def test_linear_transformation_property(
        self, patient_position, pixel_spacing, slice_thickness, image_position, 
        coordinates, scale_factor
    ):
        """
        Test linear transformation property.
        
        Property: Coordinate transformations should be linear, meaning
        T(a*v) = a*T(v) for any scalar a and vector v (excluding translation).
        """
        transformer = CoordinateTransformer(
            patient_position=patient_position,
            pixel_spacing=pixel_spacing,
            slice_thickness=slice_thickness,
            image_position=(0.0, 0.0, 0.0),  # Remove translation for linearity test
        )
        
        # Transform original coordinates
        transformed_original = transformer.dicom_to_patient(coordinates)
        
        # Transform scaled coordinates
        scaled_coordinates = coordinates * scale_factor
        transformed_scaled = transformer.dicom_to_patient(scaled_coordinates)
        
        # Verify linearity: T(a*v) should equal a*T(v)
        expected_scaled = transformed_original * scale_factor
        
        np.testing.assert_allclose(
            transformed_scaled, expected_scaled,
            rtol=1e-10, atol=1e-10,
            err_msg="Coordinate transformation should be linear (excluding translation)"
        )

    @given(
        patient_position=st.sampled_from(list(IMAGE_ORIENTATION_MAP.keys())),
        pixel_spacing=valid_pixel_spacing(),
        slice_thickness=st.floats(min_value=MIN_SLICE_THICKNESS, max_value=MAX_SLICE_THICKNESS),
        image_position=valid_image_position(),
    )
    @settings(deadline=5000)
    def test_orthogonal_axes_property(
        self, patient_position, pixel_spacing, slice_thickness, image_position
    ):
        """
        Test orthogonal axes property.
        
        Property: The row, column, and slice direction vectors should be
        orthogonal (perpendicular) to each other and normalized.
        """
        transformer = CoordinateTransformer(
            patient_position=patient_position,
            pixel_spacing=pixel_spacing,
            slice_thickness=slice_thickness,
            image_position=image_position,
        )
        
        # Get direction vectors
        row_cosines = transformer._row_cosines
        col_cosines = transformer._col_cosines
        slice_cosines = transformer._slice_cosines
        
        # Verify normalization
        assert abs(np.linalg.norm(row_cosines) - 1.0) < 1e-10, "Row cosines should be normalized"
        assert abs(np.linalg.norm(col_cosines) - 1.0) < 1e-10, "Column cosines should be normalized"
        assert abs(np.linalg.norm(slice_cosines) - 1.0) < 1e-10, "Slice cosines should be normalized"
        
        # Verify orthogonality
        assert abs(np.dot(row_cosines, col_cosines)) < 1e-10, "Row and column vectors should be orthogonal"
        assert abs(np.dot(row_cosines, slice_cosines)) < 1e-10, "Row and slice vectors should be orthogonal"
        assert abs(np.dot(col_cosines, slice_cosines)) < 1e-10, "Column and slice vectors should be orthogonal"

    @given(
        orientation=valid_orthogonal_orientation(),
        pixel_spacing=valid_pixel_spacing(),
        slice_thickness=st.floats(min_value=MIN_SLICE_THICKNESS, max_value=MAX_SLICE_THICKNESS),
        image_position=valid_image_position(),
        coordinates=valid_coordinates(),
    )
    @settings(deadline=5000)
    def test_custom_orientation_round_trip(
        self, orientation, pixel_spacing, slice_thickness, image_position, coordinates
    ):
        """
        Test round-trip accuracy with custom image orientations.
        
        Property: Round-trip transformations should work accurately for any
        valid orthogonal image orientation, not just standard patient positions.
        """
        transformer = CoordinateTransformer(
            patient_position="HFS",  # Use standard position
            image_orientation=orientation,
            pixel_spacing=pixel_spacing,
            slice_thickness=slice_thickness,
            image_position=image_position,
        )
        
        # Round-trip test: DICOM → Patient → DICOM
        patient_coords = transformer.dicom_to_patient(coordinates)
        recovered_coords = transformer.patient_to_dicom(patient_coords)
        
        np.testing.assert_allclose(
            coordinates, recovered_coords,
            rtol=1e-10, atol=1e-10,
            err_msg="Round-trip should work for custom orientations"
        )

    @given(
        coordinates=valid_coordinates(),
        pixel_spacing=valid_pixel_spacing(),
        slice_thickness=st.floats(min_value=MIN_SLICE_THICKNESS, max_value=MAX_SLICE_THICKNESS),
        image_position1=valid_image_position(),
        image_position2=valid_image_position(),
    )
    @settings(deadline=5000)
    def test_translation_invariance_property(
        self, coordinates, pixel_spacing, slice_thickness, image_position1, image_position2
    ):
        """
        Test translation invariance property.
        
        Property: The difference between two coordinate transformations should
        be equal to the difference in their image positions (translation invariance).
        """
        transformer1 = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=pixel_spacing,
            slice_thickness=slice_thickness,
            image_position=image_position1,
        )
        
        transformer2 = CoordinateTransformer(
            patient_position="HFS", 
            pixel_spacing=pixel_spacing,
            slice_thickness=slice_thickness,
            image_position=image_position2,
        )
        
        # Transform same coordinates with both transformers
        patient_coords1 = transformer1.dicom_to_patient(coordinates)
        patient_coords2 = transformer2.dicom_to_patient(coordinates)
        
        # The difference should equal the difference in image positions
        coord_difference = patient_coords2 - patient_coords1
        position_difference = np.array(image_position2) - np.array(image_position1)
        
        # For multiple coordinates, the difference should be the same for all
        if coordinates.ndim == 2:
            for i in range(len(coordinates)):
                np.testing.assert_allclose(
                    coord_difference[i], position_difference,
                    rtol=1e-10, atol=1e-10,
                    err_msg="Translation should be consistent across all coordinates"
                )
        else:
            np.testing.assert_allclose(
                coord_difference, position_difference,
                rtol=1e-10, atol=1e-10,
                err_msg="Translation difference should equal image position difference"
            )


class TestConvenienceFunctionProperties:
    """Property-based tests for convenience function invariants."""

    @given(
        patient_position=st.sampled_from(list(IMAGE_ORIENTATION_MAP.keys())),
        coordinates=valid_coordinates(),
        pixel_spacing=valid_pixel_spacing(),
        slice_thickness=st.floats(min_value=MIN_SLICE_THICKNESS, max_value=MAX_SLICE_THICKNESS),
        image_position=valid_image_position(),
    )
    @settings(deadline=5000)
    def test_convenience_function_round_trip(
        self, patient_position, coordinates, pixel_spacing, slice_thickness, image_position
    ):
        """
        Test that convenience functions produce identical results to transformer methods.
        
        Property: Convenience functions should produce identical results to using
        the CoordinateTransformer class directly.
        """
        # Use transformer class
        transformer = CoordinateTransformer(
            patient_position=patient_position,
            pixel_spacing=pixel_spacing,
            slice_thickness=slice_thickness,
            image_position=image_position,
        )
        
        transformer_patient = transformer.dicom_to_patient(coordinates)
        transformer_dicom = transformer.patient_to_dicom(coordinates)
        
        # Use convenience functions
        convenience_patient = dicom_to_patient_coordinates(
            coordinates, patient_position, pixel_spacing, slice_thickness, image_position
        )
        convenience_dicom = patient_to_dicom_coordinates(
            coordinates, patient_position, pixel_spacing, slice_thickness, image_position
        )
        
        # Verify identical results
        np.testing.assert_allclose(
            transformer_patient, convenience_patient,
            rtol=1e-15, atol=1e-15,
            err_msg="Convenience functions should match transformer methods"
        )
        
        np.testing.assert_allclose(
            transformer_dicom, convenience_dicom,
            rtol=1e-15, atol=1e-15,
            err_msg="Convenience functions should match transformer methods"
        )


class TestImageOrientationTransformProperties:
    """Property-based tests for image orientation transformation properties."""

    @given(
        from_position=st.sampled_from(list(IMAGE_ORIENTATION_MAP.keys())),
        to_position=st.sampled_from(list(IMAGE_ORIENTATION_MAP.keys())),
    )
    def test_orientation_transform_validity(self, from_position, to_position):
        """
        Test that orientation transforms produce valid orientations.
        
        Property: Transforming image orientation from one valid position to another
        should always produce a valid 6-element orientation vector.
        """
        result = transform_image_orientation(from_position, to_position)
        
        # Should be 6 elements
        assert len(result) == 6, "Image orientation should have 6 elements"
        
        # Should be same as the target position's standard orientation
        expected = IMAGE_ORIENTATION_MAP[to_position]
        assert result == expected, "Transform should produce standard orientation for target position"

    @given(
        position=st.sampled_from(list(IMAGE_ORIENTATION_MAP.keys())),
    )
    def test_orientation_identity_transform(self, position):
        """
        Test identity property of orientation transforms.
        
        Property: Transforming from a position to itself should return the same orientation.
        """
        result = transform_image_orientation(position, position)
        expected = IMAGE_ORIENTATION_MAP[position]
        
        assert result == expected, "Identity transform should return original orientation"


class TestBoundaryConditions:
    """Property-based tests for boundary condition behavior."""

    @given(
        coordinates=st.lists(
            st.lists(st.floats(min_value=-1e6, max_value=1e6), min_size=3, max_size=3),
            min_size=1,
            max_size=5
        ),
    )
    def test_extreme_coordinate_handling(self, coordinates):
        """
        Test behavior with extreme but valid coordinate values.
        
        Property: The system should handle extreme coordinate values without
        numerical overflow or underflow, maintaining precision.
        """
        coords_array = np.array(coordinates)
        
        transformer = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=(1.0, 1.0),
            slice_thickness=1.0,
            image_position=(0.0, 0.0, 0.0),
        )
        
        try:
            # Should not raise numerical errors
            patient_coords = transformer.dicom_to_patient(coords_array)
            dicom_coords = transformer.patient_to_dicom(coords_array)
            
            # Results should be finite
            assert np.all(np.isfinite(patient_coords)), "Patient coordinates should be finite"
            assert np.all(np.isfinite(dicom_coords)), "DICOM coordinates should be finite"
            
        except (OverflowError, ValueError) as e:
            pytest.fail(f"Should handle extreme coordinates without error: {e}")

    @given(
        pixel_spacing=st.tuples(
            st.floats(min_value=1e-6, max_value=1e-3),  # Very small spacing
            st.floats(min_value=1e-6, max_value=1e-3)
        ),
        slice_thickness=st.floats(min_value=1e-6, max_value=1e-3),
    )
    def test_small_spacing_precision(self, pixel_spacing, slice_thickness):
        """
        Test numerical precision with very small pixel spacings.
        
        Property: Very small pixel spacings should maintain round-trip accuracy
        within reasonable numerical tolerances.
        """
        transformer = CoordinateTransformer(
            patient_position="HFS",
            pixel_spacing=pixel_spacing,
            slice_thickness=slice_thickness,
            image_position=(0.0, 0.0, 0.0),
        )
        
        # Test with unit coordinates
        test_coords = np.array([[1.0, 1.0, 1.0]])
        
        # Round-trip test
        patient_coords = transformer.dicom_to_patient(test_coords)
        recovered_coords = transformer.patient_to_dicom(patient_coords)
        
        # Should maintain precision even with small spacings
        # Use relative tolerance appropriate for small values
        np.testing.assert_allclose(
            test_coords, recovered_coords,
            rtol=1e-6, atol=1e-12,
            err_msg="Small pixel spacings should maintain reasonable precision"
        )