"""
Performance benchmark validation for pyrt-dicom Task 2.6.2.

This module validates all performance targets are met with clinical-scale datasets,
profiles memory usage, and tests with large datasets as specified in the MVP roadmap.

Performance Targets (from MVP roadmap and Task 2.2):
- CT Series Creation: <5 seconds for 200 slices
- RT Structure Creation: <3 seconds for 20 structures  
- Memory Usage: <1GB for typical clinical datasets
- Handle clinical-scale datasets: 512x512x400 CT volumes, 20+ structures per set
"""

import pytest
import numpy as np
import time
import psutil
import os
from pathlib import Path
import tempfile
import shutil
import gc
from typing import Dict, List, Tuple

from pyrt_dicom.core.ct_series import CTSeries
from pyrt_dicom.core.rt_struct import RTStructureSet


class PerformanceProfiler:
    """Utility class for performance monitoring."""
    
    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.start_time = None
        self.start_memory = None
        
    def start_monitoring(self):
        """Start performance monitoring."""
        gc.collect()  # Clean up before measurement
        self.start_time = time.time()
        self.start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
    def stop_monitoring(self) -> Dict[str, float]:
        """Stop monitoring and return performance metrics."""
        end_time = time.time()
        end_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        return {
            'execution_time': end_time - self.start_time,
            'memory_start_mb': self.start_memory,
            'memory_end_mb': end_memory,
            'memory_delta_mb': end_memory - self.start_memory,
            'memory_peak_mb': self.process.memory_info().peak_wss / 1024 / 1024 if hasattr(self.process.memory_info(), 'peak_wss') else end_memory
        }


@pytest.fixture
def temp_benchmark_dir():
    """Create temporary directory for benchmark testing."""
    temp_dir = tempfile.mkdtemp(prefix="pyrt_benchmark_")
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def standard_ct_200_slices():
    """Create standard 200-slice CT for benchmark testing."""
    # Standard clinical CT: 200x512x512 (Z,Y,X), typical head/neck case
    z_dim, y_dim, x_dim = 200, 512, 512
    return np.random.randint(-1000, 3000, size=(z_dim, y_dim, x_dim), dtype=np.int16)


@pytest.fixture
def large_ct_400_slices():
    """Create large 400-slice CT for stress testing."""
    # Large clinical CT: 400x512x512 (Z,Y,X), whole body or high-resolution case
    z_dim, y_dim, x_dim = 400, 512, 512
    return np.random.randint(-1000, 3000, size=(z_dim, y_dim, x_dim), dtype=np.int16)


@pytest.fixture
def standard_geometric_params():
    """Standard clinical geometric parameters."""
    return {
        'pixel_spacing': [0.976562, 0.976562],  # ~1mm
        'slice_thickness': 2.5,
        'image_position': [0.0, 0.0, -250.0],
        'image_orientation': [1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
        'patient_position': 'HFS'
    }


@pytest.fixture
def twenty_structure_masks():
    """Create 20 structures for benchmark testing."""
    masks = {}
    
    # Target volumes (5 structures)
    target_structures = ['GTV_Primary', 'CTV_6000', 'CTV_5400', 'PTV_6000', 'PTV_5400']
    for i, name in enumerate(target_structures):
        mask = np.zeros((200, 512, 512), dtype=bool)
        # Create overlapping target volumes of different sizes
        margin = i * 5 + 10
        center_z, center_y, center_x = 100, 256, 256
        
        z_start = max(0, center_z - margin)
        z_end = min(200, center_z + margin)
        y_start = max(0, center_y - margin) 
        y_end = min(512, center_y + margin)
        x_start = max(0, center_x - margin)  
        x_end = min(512, center_x + margin)
        
        mask[z_start:z_end, y_start:y_end, x_start:x_end] = True
        masks[name] = mask
    
    # Organs at risk (15 structures)
    oar_positions = [
        ('SpinalCord', (100, 256, 400)),
        ('Brainstem', (80, 256, 380)),
        ('Parotid_L', (120, 200, 300)),
        ('Parotid_R', (120, 312, 300)),
        ('Mandible', (140, 256, 350)),
        ('Larynx', (130, 256, 320)),
        ('Esophagus', (110, 256, 340)),
        ('Heart', (150, 256, 200)),
        ('Lung_L', (120, 200, 200)),
        ('Lung_R', (120, 312, 200)),
        ('Liver', (140, 350, 200)),
        ('Kidney_L', (160, 200, 180)),
        ('Kidney_R', (160, 312, 180)),
        ('Bowel', (170, 256, 160)),
        ('Bladder', (180, 256, 140))
    ]
    
    for name, (z_center, y_center, x_center) in oar_positions:
        mask = np.zeros((200, 512, 512), dtype=bool)
        size = 20  # Standard organ size
        
        z_start = max(0, z_center - size)
        z_end = min(200, z_center + size) 
        y_start = max(0, y_center - size)
        y_end = min(512, y_center + size)
        x_start = max(0, x_center - size)
        x_end = min(512, x_center + size)
        
        mask[z_start:z_end, y_start:y_end, x_start:x_end] = True
        masks[name] = mask
    
    return masks


class TestCTPerformanceBenchmarks:
    """Test CT creation performance benchmarks."""
    
    def test_ct_200_slice_performance_target(self, standard_ct_200_slices, standard_geometric_params, temp_benchmark_dir):
        """Test CT creation <5 seconds for 200 slices (MVP requirement)."""
        
        profiler = PerformanceProfiler()
        profiler.start_monitoring()
        
        # Create CT series
        ct_series = CTSeries.from_array(
            pixel_array=standard_ct_200_slices,
            pixel_spacing=standard_geometric_params['pixel_spacing'],
            slice_thickness=standard_geometric_params['slice_thickness'],
            patient_info={
                'PatientID': 'PERF_CT_200',
                'PatientName': 'Performance^Test^CT200'
            }
        )
        
        # Save series (part of the workflow performance)
        output_dir = temp_benchmark_dir / "ct_200_performance"
        output_dir.mkdir()
        ct_paths = ct_series.save_series(output_dir)
        
        metrics = profiler.stop_monitoring()
        
        # Verify performance targets
        assert metrics['execution_time'] < 5.0, f"CT 200-slice creation took {metrics['execution_time']:.2f}s, expected <5s"
        assert len(ct_paths) == 200, f"Expected 200 files, got {len(ct_paths)}"
        assert metrics['memory_delta_mb'] < 500, f"Memory usage {metrics['memory_delta_mb']:.1f}MB too high for 200-slice CT"
        
        print(f"✅ CT 200-slice performance target met:")
        print(f"   - Execution time: {metrics['execution_time']:.2f}s (target: <5s)")
        print(f"   - Memory delta: {metrics['memory_delta_mb']:.1f}MB")
        print(f"   - Files created: {len(ct_paths)}")
    
    def test_ct_400_slice_stress_test(self, large_ct_400_slices, standard_geometric_params, temp_benchmark_dir):
        """Test CT creation with large 400-slice dataset (stress test)."""
        
        profiler = PerformanceProfiler()
        profiler.start_monitoring()
        
        # Create large CT series
        ct_series = CTSeries.from_array(
            pixel_array=large_ct_400_slices,
            pixel_spacing=standard_geometric_params['pixel_spacing'],
            slice_thickness=standard_geometric_params['slice_thickness'],
            patient_info={
                'PatientID': 'STRESS_CT_400',
                'PatientName': 'Stress^Test^CT400'
            }
        )
        
        # Save first 10 slices for performance testing (don't need to save all 400)
        output_dir = temp_benchmark_dir / "ct_400_stress"
        output_dir.mkdir()
        
        # Test creating and saving subset for performance measurement
        subset_paths = []
        for i in range(10):  # Save 10 slices as representative sample
            slice_path = output_dir / f"ct_slice_{i:03d}.dcm"
            single_slice_series = CTSeries.from_array(
                pixel_array=large_ct_400_slices[i:i+1],  # Single slice
                pixel_spacing=standard_geometric_params['pixel_spacing'],
                slice_thickness=standard_geometric_params['slice_thickness'],
                patient_info={
                    'PatientID': 'STRESS_CT_400',
                    'PatientName': 'Stress^Test^CT400'
                }
            )
            saved_path = single_slice_series.save(slice_path)
            subset_paths.append(saved_path)
        
        metrics = profiler.stop_monitoring()
        
        # Stress test targets (more lenient for 400 slices)
        assert metrics['execution_time'] < 15.0, f"CT 400-slice processing took {metrics['execution_time']:.2f}s, expected <15s"
        assert metrics['memory_delta_mb'] < 1000, f"Memory usage {metrics['memory_delta_mb']:.1f}MB too high for 400-slice CT"
        assert len(subset_paths) == 10
        
        print(f"✅ CT 400-slice stress test passed:")
        print(f"   - Processing time: {metrics['execution_time']:.2f}s")
        print(f"   - Memory delta: {metrics['memory_delta_mb']:.1f}MB")
        print(f"   - Sample files created: {len(subset_paths)}")
    
    def test_ct_memory_efficiency(self, standard_ct_200_slices, standard_geometric_params):
        """Test CT creation memory efficiency."""
        
        profiler = PerformanceProfiler() 
        profiler.start_monitoring()
        
        # Create multiple CT series to test memory cleanup
        for i in range(3):
            ct_series = CTSeries.from_array(
                pixel_array=standard_ct_200_slices,
                pixel_spacing=standard_geometric_params['pixel_spacing'],
                slice_thickness=standard_geometric_params['slice_thickness'],
                patient_info={
                    'PatientID': f'MEM_TEST_{i:03d}',
                    'PatientName': f'Memory^Test^{i:03d}'
                }
            )
            
            # Force garbage collection between iterations
            del ct_series
            gc.collect()
        
        metrics = profiler.stop_monitoring()
        
        # Memory should not accumulate significantly across iterations
        assert metrics['memory_delta_mb'] < 300, f"Memory accumulation {metrics['memory_delta_mb']:.1f}MB suggests memory leak"
        
        print(f"✅ CT memory efficiency test passed:")
        print(f"   - Total memory delta: {metrics['memory_delta_mb']:.1f}MB (3 iterations)")
        print(f"   - Average per iteration: {metrics['memory_delta_mb']/3:.1f}MB")


class TestRTStructPerformanceBenchmarks:
    """Test RT Structure Set performance benchmarks."""
    
    def test_rt_struct_20_structures_performance_target(self, standard_ct_200_slices, twenty_structure_masks, 
                                                       standard_geometric_params, temp_benchmark_dir):
        """Test RT Structure creation <3 seconds for 20 structures (MVP requirement)."""
        
        # First create CT reference
        ct_series = CTSeries.from_array(
            pixel_array=standard_ct_200_slices,
            pixel_spacing=standard_geometric_params['pixel_spacing'],
            slice_thickness=standard_geometric_params['slice_thickness'],
            patient_info={'PatientID': 'PERF_STRUCT_20', 'PatientName': 'Performance^Struct^20'}
        )
        
        ct_dir = temp_benchmark_dir / "ct_reference"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = __import__('pydicom').dcmread(ct_paths[0])
        
        # Benchmark RT Structure creation
        profiler = PerformanceProfiler()
        profiler.start_monitoring()
        
        rt_struct = RTStructureSet.from_masks(
            ct_reference=reference_ct,
            masks=twenty_structure_masks,
            patient_info={'PatientID': 'PERF_STRUCT_20', 'PatientName': 'Performance^Struct^20'}
        )
        
        # Save structure set (part of workflow performance)
        struct_path = temp_benchmark_dir / "struct_20_performance.dcm"
        saved_path = rt_struct.save(struct_path)
        
        metrics = profiler.stop_monitoring()
        
        # Verify performance targets
        assert metrics['execution_time'] < 3.0, f"RT Structure 20-structure creation took {metrics['execution_time']:.2f}s, expected <3s"
        assert saved_path.exists()
        assert metrics['memory_delta_mb'] < 200, f"Memory usage {metrics['memory_delta_mb']:.1f}MB too high for 20 structures"
        
        # Verify all structures were created
        reloaded = __import__('pydicom').dcmread(saved_path)
        assert len(reloaded.StructureSetROISequence) == 20
        
        print(f"✅ RT Structure 20-structure performance target met:")
        print(f"   - Execution time: {metrics['execution_time']:.2f}s (target: <3s)")
        print(f"   - Memory delta: {metrics['memory_delta_mb']:.1f}MB")
        print(f"   - Structures created: {len(reloaded.StructureSetROISequence)}")
    
    def test_contour_processing_performance(self, twenty_structure_masks):
        """Test mask-to-contour conversion performance."""
        
        from pyrt_dicom.utils.contour_processing import MaskToContourConverter
        
        profiler = PerformanceProfiler()
        profiler.start_monitoring()
        
        converter = MaskToContourConverter()
        total_contours = 0
        
        # Process all 20 structures
        for name, mask in twenty_structure_masks.items():
            contours = converter.convert_mask_to_contours(mask)
            total_contours += len(contours)
        
        metrics = profiler.stop_monitoring()
        
        # Contour processing should be efficient
        assert metrics['execution_time'] < 2.0, f"Contour processing took {metrics['execution_time']:.2f}s, expected <2s"
        assert total_contours > 0, "No contours generated"
        assert metrics['memory_delta_mb'] < 100, f"Contour processing memory usage {metrics['memory_delta_mb']:.1f}MB too high"
        
        print(f"✅ Contour processing performance passed:")
        print(f"   - Processing time: {metrics['execution_time']:.2f}s")
        print(f"   - Total contours: {total_contours}")
        print(f"   - Memory delta: {metrics['memory_delta_mb']:.1f}MB")


class TestMemoryProfileBenchmarks:
    """Test memory usage profiling and optimization."""
    
    def test_typical_clinical_dataset_memory_usage(self, standard_ct_200_slices, twenty_structure_masks, 
                                                  standard_geometric_params, temp_benchmark_dir):
        """Test memory usage <1GB for typical clinical datasets (MVP requirement)."""
        
        profiler = PerformanceProfiler()
        profiler.start_monitoring()
        
        # Complete typical clinical workflow
        # 1. Create CT series
        ct_series = CTSeries.from_array(
            pixel_array=standard_ct_200_slices,
            pixel_spacing=standard_geometric_params['pixel_spacing'],
            slice_thickness=standard_geometric_params['slice_thickness'],
            patient_info={'PatientID': 'MEM_CLINICAL', 'PatientName': 'Memory^Clinical^Test'}
        )
        
        # 2. Save CT series
        ct_dir = temp_benchmark_dir / "memory_test_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = __import__('pydicom').dcmread(ct_paths[0])
        
        # 3. Create RT Structure Set
        rt_struct = RTStructureSet.from_masks(
            ct_reference=reference_ct,
            masks=twenty_structure_masks,
            patient_info={'PatientID': 'MEM_CLINICAL', 'PatientName': 'Memory^Clinical^Test'}
        )
        
        # 4. Save RT Structure Set
        struct_path = temp_benchmark_dir / "memory_test_struct.dcm"
        rt_struct.save(struct_path)
        
        metrics = profiler.stop_monitoring()
        
        # Memory target: <1GB for typical clinical datasets
        total_memory_mb = metrics['memory_end_mb']
        assert total_memory_mb < 1024, f"Total memory usage {total_memory_mb:.1f}MB exceeds 1GB limit"
        assert metrics['memory_delta_mb'] < 800, f"Memory increase {metrics['memory_delta_mb']:.1f}MB too high"
        
        print(f"✅ Typical clinical dataset memory usage within limits:")
        print(f"   - Total memory: {total_memory_mb:.1f}MB (limit: <1024MB)")
        print(f"   - Memory delta: {metrics['memory_delta_mb']:.1f}MB")
        print(f"   - CT slices: {len(ct_paths)}")
        print(f"   - Structures: {len(twenty_structure_masks)}")
    
    def test_large_dataset_memory_profile(self, large_ct_400_slices, standard_geometric_params):
        """Test memory profiling with large datasets."""
        
        profiler = PerformanceProfiler()
        profiler.start_monitoring()
        
        # Process large dataset in chunks to manage memory
        chunk_size = 50  # Process 50 slices at a time
        total_chunks = large_ct_400_slices.shape[0] // chunk_size
        
        for i in range(total_chunks):
            start_idx = i * chunk_size
            end_idx = start_idx + chunk_size
            
            chunk_array = large_ct_400_slices[start_idx:end_idx]
            
            ct_chunk = CTSeries.from_array(
                pixel_array=chunk_array,
                pixel_spacing=standard_geometric_params['pixel_spacing'],
                slice_thickness=standard_geometric_params['slice_thickness'],
                patient_info={
                    'PatientID': f'LARGE_CHUNK_{i:03d}',
                    'PatientName': f'Large^Chunk^{i:03d}'
                }
            )
            
            # Clean up after each chunk
            del ct_chunk
            gc.collect()
        
        metrics = profiler.stop_monitoring()
        
        # Large dataset processing should not consume excessive memory
        assert metrics['memory_delta_mb'] < 500, f"Large dataset memory delta {metrics['memory_delta_mb']:.1f}MB too high"
        
        print(f"✅ Large dataset memory profile acceptable:")
        print(f"   - Total slices processed: {large_ct_400_slices.shape[0]}")
        print(f"   - Chunks processed: {total_chunks}")
        print(f"   - Memory delta: {metrics['memory_delta_mb']:.1f}MB")


class TestScalabilityBenchmarks:
    """Test scalability with increasing dataset sizes."""
    
    @pytest.mark.parametrize("num_slices", [50, 100, 200, 300])
    def test_ct_scalability_by_slice_count(self, num_slices, standard_geometric_params):
        """Test CT creation scalability with increasing slice counts."""
        
        # Create CT array of specified size
        ct_array = np.random.randint(-1000, 3000, size=(num_slices, 256, 256), dtype=np.int16)
        
        profiler = PerformanceProfiler()
        profiler.start_monitoring()
        
        ct_series = CTSeries.from_array(
            pixel_array=ct_array,
            pixel_spacing=standard_geometric_params['pixel_spacing'],
            slice_thickness=standard_geometric_params['slice_thickness'],
            patient_info={
                'PatientID': f'SCALE_{num_slices:03d}',
                'PatientName': f'Scalability^Test^{num_slices:03d}'
            }
        )
        
        metrics = profiler.stop_monitoring()
        
        # Performance should scale reasonably (not exponentially)
        time_per_slice = metrics['execution_time'] / num_slices
        memory_per_slice = metrics['memory_delta_mb'] / num_slices
        
        assert time_per_slice < 0.05, f"Time per slice {time_per_slice:.4f}s too high for {num_slices} slices"
        assert memory_per_slice < 2.0, f"Memory per slice {memory_per_slice:.2f}MB too high for {num_slices} slices"
        
        print(f"✅ {num_slices}-slice scalability: {time_per_slice:.4f}s/slice, {memory_per_slice:.2f}MB/slice")
    
    @pytest.mark.parametrize("num_structures", [5, 10, 20, 30])  
    def test_structure_scalability_by_count(self, num_structures, standard_ct_200_slices, 
                                          standard_geometric_params, temp_benchmark_dir):
        """Test RT Structure creation scalability with increasing structure counts."""
        
        # Create specified number of structures
        masks = {}
        for i in range(num_structures):
            mask = np.zeros((200, 512, 512), dtype=bool)
            # Create non-overlapping structures
            y_offset = (i % 10) * 40
            x_offset = ((i // 10) % 10) * 40
            
            mask[80:120, 100+y_offset:140+y_offset, 100+x_offset:140+x_offset] = True
            masks[f'Structure_{i:03d}'] = mask
        
        # Create CT reference
        ct_series = CTSeries.from_array(
            pixel_array=standard_ct_200_slices,
            pixel_spacing=standard_geometric_params['pixel_spacing'],
            slice_thickness=standard_geometric_params['slice_thickness'],
            patient_info={'PatientID': f'STRUCT_SCALE_{num_structures}', 'PatientName': 'Struct^Scale^Test'}
        )
        
        ct_dir = temp_benchmark_dir / f"struct_scale_{num_structures}"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = __import__('pydicom').dcmread(ct_paths[0])
        
        # Benchmark structure creation
        profiler = PerformanceProfiler()
        profiler.start_monitoring()
        
        rt_struct = RTStructureSet.from_masks(
            ct_reference=reference_ct,
            masks=masks,
            patient_info={'PatientID': f'STRUCT_SCALE_{num_structures}', 'PatientName': 'Struct^Scale^Test'}
        )
        
        metrics = profiler.stop_monitoring()
        
        # Performance should scale reasonably
        time_per_structure = metrics['execution_time'] / num_structures
        memory_per_structure = metrics['memory_delta_mb'] / num_structures
        
        assert time_per_structure < 0.2, f"Time per structure {time_per_structure:.3f}s too high for {num_structures} structures"
        assert memory_per_structure < 10.0, f"Memory per structure {memory_per_structure:.2f}MB too high for {num_structures} structures"
        
        print(f"✅ {num_structures}-structure scalability: {time_per_structure:.3f}s/struct, {memory_per_structure:.2f}MB/struct")


if __name__ == "__main__":
    # Run performance benchmarks
    pytest.main([__file__, "-v", "-s"])