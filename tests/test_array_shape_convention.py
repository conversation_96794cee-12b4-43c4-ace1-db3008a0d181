"""
Test 3D Numpy Array Shape Convention Enforcement.

Tests the critical (Z, Y, X) = (slices, rows, cols) convention enforcement
for all 3D medical imaging arrays in the codebase.
"""

import pytest
import numpy as np
from pyrt_dicom.validation.geometric import (
    validate_3d_array_shape_convention,
    enforce_3d_array_shape_convention
)
from pyrt_dicom.utils.exceptions import CoordinateSystemError


class TestArrayShapeConvention:
    """Test 3D array shape convention validation and enforcement."""

    def test_correct_zyx_array_validation(self):
        """Test that correctly shaped (Z, Y, X) arrays pass validation."""
        # Correct CT-like array: (100 slices, 512 rows, 512 cols)
        correct_array = np.random.randint(-1000, 3000, (100, 512, 512), dtype=np.int16)
        
        errors = validate_3d_array_shape_convention(correct_array, "CT array")
        assert len(errors) == 0, f"Valid (Z, Y, X) array should not have errors: {errors}"

    def test_incorrect_yxz_array_validation(self):
        """Test that incorrectly shaped (Y, X, Z) arrays are caught."""
        # Incorrect array: (512 rows, 512 cols, 100 slices) - WRONG!
        incorrect_array = np.random.randint(-1000, 3000, (512, 512, 100), dtype=np.int16)
        
        errors = validate_3d_array_shape_convention(incorrect_array, "CT array")
        assert len(errors) > 0, "Invalid (Y, X, Z) array should have validation errors"
        
        # Check that the error message mentions the pattern detection
        error_text = " ".join(errors)
        assert "(Y, X, Z)" in error_text, "Error should mention (Y, X, Z) pattern"

    def test_enforce_correct_array_no_exception(self):
        """Test that enforce function doesn't raise exception for correct arrays."""
        correct_array = np.random.randint(-1000, 3000, (50, 256, 256), dtype=np.int16)
        
        # Should not raise any exception
        enforce_3d_array_shape_convention(correct_array, "Dose array")

    def test_enforce_incorrect_array_raises_exception(self):
        """Test that enforce function raises exception for incorrect arrays."""
        # Wrong shape: (256, 256, 50) instead of (50, 256, 256)
        incorrect_array = np.random.rand(256, 256, 50)
        
        with pytest.raises(CoordinateSystemError) as exc_info:
            enforce_3d_array_shape_convention(incorrect_array, "Dose array")
        
        # Verify the exception message contains useful information
        error_message = str(exc_info.value)
        assert "3D Array Shape Convention Violation" in error_message
        assert "CRITICAL" in error_message
        assert "(Z, Y, X)" in error_message

    def test_2d_array_validation(self):
        """Test that 2D arrays are handled appropriately."""
        array_2d = np.random.rand(512, 512)
        
        errors = validate_3d_array_shape_convention(array_2d, "2D array")
        assert len(errors) > 0, "2D array should be flagged as not 3D"
        
        assert "must be 3D array" in errors[0]

    def test_non_numpy_array_validation(self):
        """Test validation of non-numpy array inputs."""
        python_list = [[[1, 2], [3, 4]], [[5, 6], [7, 8]]]
        
        errors = validate_3d_array_shape_convention(python_list, "Python list")
        assert len(errors) > 0
        assert "must be numpy.ndarray" in errors[0]

    def test_zero_dimension_array_validation(self):
        """Test arrays with zero-sized dimensions."""
        zero_array = np.zeros((0, 64, 64))
        
        errors = validate_3d_array_shape_convention(zero_array, "Zero array")
        assert len(errors) > 0
        assert "zero-sized dimension" in errors[0]

    def test_clinical_dose_array_shape(self):
        """Test typical clinical dose array shapes are accepted."""
        # Typical dose array: fewer slices, smaller resolution
        dose_array = np.random.rand(50, 128, 128)
        
        errors = validate_3d_array_shape_convention(dose_array, "Dose array")
        assert len(errors) == 0, f"Clinical dose array should be valid: {errors}"

    def test_clinical_structure_mask_shape(self):
        """Test typical clinical structure mask shapes are accepted."""
        # Structure mask matching CT
        mask_array = np.zeros((100, 512, 512), dtype=bool)
        mask_array[40:60, 200:300, 200:300] = True  # Some structure
        
        errors = validate_3d_array_shape_convention(mask_array, "Structure mask")
        assert len(errors) == 0, f"Clinical structure mask should be valid: {errors}"

    def test_unusual_but_valid_dimensions(self):
        """Test unusual but potentially valid array dimensions."""
        # Very thin slices but reasonable
        thin_array = np.random.rand(5, 1024, 1024)
        
        errors = validate_3d_array_shape_convention(thin_array, "Thin slice array")
        # Should have warnings but not be completely invalid
        if errors:
            # Check that it's a warning about unusual dimensions, not format error
            error_text = " ".join(errors)
            assert "unusual" in error_text.lower() or "verify" in error_text.lower()

    def test_disable_convention_enforcement(self):
        """Test disabling convention enforcement."""
        # This would normally trigger an error
        wrong_array = np.random.rand(512, 512, 100)
        
        # With enforcement disabled, should not get convention errors
        errors = validate_3d_array_shape_convention(wrong_array, "Test array", enforce_zyx=False)
        
        # Should still get basic validation but not convention-specific errors
        convention_errors = [e for e in errors if "(Y, X, Z)" in e or "convention" in e.lower()]
        assert len(convention_errors) == 0, "Convention enforcement should be disabled"

    def test_large_array_warning(self):
        """Test that very large arrays generate appropriate warnings."""
        # Array with suspiciously large first dimension
        large_array = np.random.rand(5000, 64, 64)
        
        errors = validate_3d_array_shape_convention(large_array, "Large array")
        assert len(errors) > 0
        
        error_text = " ".join(errors)
        assert "large" in error_text.lower() or "unusual" in error_text.lower()


class TestIntegrationWithCoreClasses:
    """Test that core classes properly enforce the convention."""

    def test_ct_series_enforces_convention(self):
        """Test that CTSeries enforces array shape convention."""
        from pyrt_dicom.core.ct_series import CTSeries
        
        # Wrong array shape should raise exception during creation
        wrong_ct_array = np.random.randint(-1000, 3000, (512, 512, 100), dtype=np.int16)
        
        with pytest.raises(CoordinateSystemError):
            CTSeries.from_array(
                pixel_array=wrong_ct_array,
                pixel_spacing=(1.0, 1.0),
                slice_thickness=2.5
            )

    def test_mask_to_contour_enforces_convention(self):
        """Test that MaskToContourConverter enforces convention."""
        from pyrt_dicom.utils.contour_processing import MaskToContourConverter
        
        converter = MaskToContourConverter()
        
        # Wrong mask shape should raise exception
        wrong_mask = np.ones((64, 64, 32), dtype=bool)  # (Y, X, Z) - WRONG!
        
        with pytest.raises(CoordinateSystemError):
            converter.convert_mask_to_contours(wrong_mask)

    def test_correct_shapes_work_with_core_classes(self):
        """Test that correct shapes work properly with core classes."""
        from pyrt_dicom.core.ct_series import CTSeries
        from pyrt_dicom.utils.contour_processing import MaskToContourConverter
        
        # Correct shapes should work
        correct_ct = np.random.randint(-1000, 3000, (10, 64, 64), dtype=np.int16)
        correct_mask = np.ones((10, 64, 64), dtype=bool)
        
        # These should not raise exceptions
        ct_series = CTSeries.from_array(
            pixel_array=correct_ct,
            pixel_spacing=(1.0, 1.0),
            slice_thickness=2.5
        )
        
        converter = MaskToContourConverter()
        contours = converter.convert_mask_to_contours(correct_mask)
        
        # Basic sanity checks
        assert ct_series is not None
        assert isinstance(contours, list)