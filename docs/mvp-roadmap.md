# pyrt-dicom (Pirate DICOM) MVP: Project Requirements Document

## Executive Summary

**Vision**: Create a focused Python library specifically for **creating and writing** radiotherapy DICOM files, filling the critical gap in the existing ecosystem where most tools excel at reading but struggle with robust DICOM creation.

**Project Name**: **pyrt-dicom** ("Pirate DICOM") - A play on "py-RT-DICOM" that's memorable and distinct

**Core Value Proposition**: Be the definitive tool for RT DICOM creation in Python, learning from PyMedPhys._pinnacle's proven patterns while providing a cleaner, more focused API specifically designed for creating CT, RTSTRUCT, RTDOSE, and RTPLAN files from common data sources.

**Strategic Positioning**: Complement existing tools (PyMedPhys for analysis, scikit-rt for research) by becoming the go-to library for DICOM RT file creation with future integration pathways.

## Research-Based Design Decisions

### Learning from PyMedPhys._pinnacle

From analyzing PyMedPhys's Pinnacle export tool, we've identified key patterns to adopt and improve upon:

**Proven Patterns to Adopt**:
- **UID Management**: PyMedPhys uses both HASH-based and random UID generation - we'll implement similar with clinical-friendly defaults
- **Coordinate System Handling**: PyMedPhys handles Pinnacle→DICOM coordinate transformations - we'll generalize this for any input format
- **Modular Export Design**: Separate export methods for each DICOM type (export_image, export_rtplan, export_rtstruct, export_rtdose)
- **Clinical Validation**: PyMedPhys validates patient positions and geometric constraints - we'll expand this
- **pydicom Foundation**: Built on pydicom with proper DICOM compliance - essential pattern to follow

**Areas for Improvement**:
- **Input Source Limitation**: PyMedPhys is tied to Pinnacle format - we'll support generic NumPy arrays, masks, and common formats
- **API Complexity**: PyMedPhys requires deep knowledge of Pinnacle structure - we'll provide intuitive, clinical-focused APIs
- **Documentation Gap**: Limited examples for creation workflows - we'll prioritize comprehensive examples
- **Error Handling**: Basic error reporting - we'll implement comprehensive clinical validation with helpful guidance
- **Privacy Protection**: Limited anonymization capabilities - we'll implement comprehensive DICOM anonymization and de-identification

### Competitive Advantages

**vs. PyMedPhys._pinnacle**:
- ✅ **Input Flexibility**: Support any data source, not just Pinnacle
- ✅ **Focused API**: Purpose-built for creation, not conversion
- ✅ **Better Documentation**: Comprehensive examples and clinical context
- ✅ **Clinical Validation**: Extensive safety checks and helpful error messages

**vs. scikit-rt**:
- ✅ **Creation Focus**: Specialized for writing DICOM files, not just reading/analysis
- ✅ **Simpler Dependencies**: Focused toolkit without research-heavy dependencies
- ✅ **Clinical API**: Designed for medical physicists, not researchers

**vs. RT-utils**:
- ✅ **Complete Coverage**: All 4 RT DICOM types, not just RTSTRUCT
- ✅ **Advanced Features**: Better coordinate handling, validation, and clinical safety
- ✅ **Privacy Protection**: Built-in anonymization and de-identification capabilities

## Technical Architecture

### Foundation Design

**Core Philosophy**: "Batteries included for RT DICOM creation"
```python
# Simple, predictable API inspired by PyMedPhys patterns but generalized
import pyrt_dicom as prt

# Create from common data sources with intelligent defaults
rt_struct = prt.RTStructureSet.from_masks(
    ct_reference=ct_images,
    masks={'PTV': ptv_mask, 'OAR_Heart': heart_mask},
    colors=['red', 'blue']  # Clinical defaults if not specified
)

rt_dose = prt.RTDose.from_array(
    dose_array=dose_3d,
    reference_image=ct_images,
    dose_units='Gy'
)

rt_plan = prt.RTPlan.from_beam_config(
    prescription=prescription_dict,
    beams=beam_configs,
    reference_dose=rt_dose
)

# Save with automatic validation
rt_struct.save('output_struct.dcm')
rt_dose.save('output_dose.dcm') 
rt_plan.save('output_plan.dcm')
```

### Module Architecture

```python
pyrt_dicom/
├── __init__.py              # Clean public API
├── core/                    # Core DICOM objects
│   ├── base.py             # Base DICOM creation class
│   ├── ct_series.py        # CT image series creation
│   ├── rt_struct.py        # RT Structure Set creation
│   ├── rt_dose.py          # RT Dose creation (MAJOR GAP in ecosystem)
│   └── rt_plan.py          # RT Plan creation (MAJOR GAP in ecosystem)
├── builders/               # High-level builders inspired by RT-utils pattern
│   ├── struct_builder.py   # Structure set builder
│   ├── dose_builder.py     # Dose distribution builder
│   └── plan_builder.py     # Treatment plan builder
├── validation/             # Clinical and technical validation
│   ├── clinical.py         # Clinical range and safety validation
│   ├── dicom_compliance.py # DICOM standard compliance
│   └── geometric.py        # Coordinate system validation
├── anonymization/          # DICOM anonymization and privacy protection
│   ├── anonymizer.py       # Core anonymization engine
│   ├── profiles.py         # Anonymization profiles (basic, clinical, research)
│   └── audit.py           # Anonymization audit logging
├── coordinates/            # Coordinate system handling (learned from PyMedPhys)
│   ├── transforms.py       # Coordinate transformations
│   └── reference_frames.py # Frame of reference management
├── uid_generation/         # UID management (inspired by PyMedPhys)
│   ├── generators.py       # UID generation strategies
│   ├── registry.py         # UID relationship tracking
│   └── regenerator.py      # Comprehensive UID regeneration for DICOM files
├── templates/              # DICOM templates for different modalities
│   ├── ct_template.py      # CT Image IOD template
│   ├── struct_template.py  # RT Structure Set IOD template
│   ├── dose_template.py    # RT Dose IOD template
│   └── plan_template.py    # RT Plan IOD template
├── utils/                  # Utilities
│   ├── exceptions.py       # Custom exception hierarchy
│   ├── logging.py          # Clinical audit logging
│   └── helpers.py          # Common helper functions
└── integrations/           # Future Enhancements
    └── scikit_rt.py        # Interface layer for scikit-rt integration
```

### Command Line Interface (CLI)
A command-line interface will be developed to provide convenient access to common pyrt-dicom functionality. The CLI will support:
- Batch processing of DICOM file creation
- Simple conversion between different DICOM formats
- Basic validation and verification of DICOM files
- Integration with existing DICOM tools and workflows

*Note: CLI development is not a priority for the initial MVP release but is planned for a future update.*


### Core Data Structures

**🔸 Critical Convention: 3D Numpy Array Shape**

**ALL 3D numpy arrays in this codebase MUST follow the (Z, Y, X) convention:**
- **Z dimension (index 0)**: Slice/image index (e.g., axial slices in CT)
- **Y dimension (index 1)**: Image rows (superior-inferior in patient coordinates)  
- **X dimension (index 2)**: Image columns (left-right in patient coordinates)

```python
# ✅ CORRECT: 3D CT array shape (Z, Y, X) = (slices, rows, columns)
ct_array = np.random.randint(-1000, 3000, (100, 512, 512), dtype=np.int16)  # 100 slices, 512x512 pixels each
dose_array = np.random.rand(50, 256, 256) * 70.0  # 50 slices, 256x256 dose grid

# ❌ INCORRECT: Any other convention
ct_array = np.random.randint(-1000, 3000, (512, 512, 100), dtype=np.int16)  # Wrong! This is (Y, X, Z)
```

**Clinical Rationale:**
- Ensures consistent coordinate system handling across all RT objects
- Prevents orientation errors that could affect treatment planning accuracy
- Maintains compatibility with DICOM Patient Coordinate System (LPS)
- Enables safe integration with other medical imaging libraries
- Critical for patient safety in radiotherapy applications

**Base DICOM Creator Pattern** (inspired by PyMedPhys structure):
```python
class BaseDicomCreator:
    """Base class for all DICOM RT creators"""
    
    def __init__(self, reference_image=None, patient_info=None):
        self.reference_image = reference_image
        self.patient_info = patient_info or {}
        self._uid_generator = UIDGenerator()
        self._validator = DicomValidator()
        
    def _create_base_dataset(self):
        """Create base DICOM dataset with required elements"""
        
    def _set_patient_info(self, dataset, anonymize=False):
        """Set patient information with optional anonymization"""
        
    def _set_study_info(self, dataset):
        """Set study and series information"""
        
    def validate(self):
        """Comprehensive validation before saving"""
        
    def save(self, filepath, validate=True, anonymize=False, anonymization_profile='basic'):
        """Save with optional validation and anonymization"""

class RTStructureSet(BaseDicomCreator):
    """RT Structure Set creation with mask/contour input support"""
    
    @classmethod
    def from_masks(cls, ct_reference, masks, names=None, colors=None):
        """Create from binary masks - most common use case"""
        
    @classmethod  
    def from_contours(cls, ct_reference, contours, names=None, colors=None):
        """Create from contour point data"""
        
    def add_structure(self, mask_or_contour, name, color=None):
        """Add individual structure"""
        
    def _masks_to_contours(self, masks):
        """Convert masks to DICOM contour sequences"""

class RTDose(BaseDicomCreator):
    """RT Dose creation - MAJOR MISSING CAPABILITY in ecosystem"""
    
    @classmethod
    def from_array(cls, dose_array, reference_image, dose_units='Gy', 
                   dose_type='PHYSICAL', summation_type='PLAN'):
        """Create from 3D dose array - primary use case"""
        
    def _create_dose_grid(self, dose_array):
        """Create proper DICOM dose grid with scaling"""
        
    def _set_dose_metadata(self, dose_units, dose_type, summation_type):
        """Set dose-specific DICOM metadata"""

class RTPlan(BaseDicomCreator):
    """RT Plan creation - MAJOR MISSING CAPABILITY in ecosystem"""
    
    @classmethod
    def from_beam_config(cls, prescription, beams, reference_dose=None):
        """Create from beam configuration data"""
        
    def add_beam(self, beam_config):
        """Add individual beam to plan"""
        
    def _create_beam_sequence(self, beams):
        """Create DICOM beam sequence"""
```

## Implementation Strategy

### Phase 1: Foundation (Months 1-2)

**Objectives**: Establish core architecture and basic CT/Structure creation

**Key Deliverables**:
- Core base classes with UID generation and validation framework
- CT image series creation from NumPy arrays
- RT Structure Set creation from binary masks
- PyMedPhys-style coordinate system handling
- Comprehensive unit test framework

**Technical Priorities**:
```python
# Month 1: Foundation and CT Creation
- Implement BaseDicomCreator with pydicom integration
- Create UIDGenerator with HASH and random strategies (PyMedPhys pattern)
- Create DICOMUIDRegenerator for comprehensive UID management across multiple files
- Implement CTSeries.from_array() with geometric validation
- Coordinate system transformation framework
- Basic clinical validation (dose ranges, geometric limits)

# Month 2: Structure Set Creation  
- Implement RTStructureSet.from_masks() 
- Mask-to-contour conversion with sub-pixel accuracy
- Structure naming and color management
- DICOM IOD compliance validation
- Integration testing with real clinical data
```

**Success Criteria**:
- Create valid CT series from NumPy arrays in <5 seconds
- Generate RT Structure Sets that load in commercial TPS systems
- Pass DICOM compliance validation for CT and RTSTRUCT
- Handle coordinate transformations with <1mm accuracy

### Phase 2: Dose Creation (Month 3)

**Objectives**: Implement RT Dose creation - the critical missing capability

**Key Deliverables**:
- RT Dose creation from 3D dose arrays
- Proper dose grid scaling and multi-frame DICOM structure
- Integration with existing CT and Structure references
- Dose summation type handling

**Technical Focus**:
```python
# RT Dose Implementation - Learning from PyMedPhys dose handling
class RTDose(BaseDicomCreator):
    @classmethod
    def from_array(cls, dose_array, reference_image, **kwargs):
        # Implement dose grid scaling (PyMedPhys: dose = pixel_array * DoseGridScaling)
        # Create multi-frame pixel data for large arrays
        # Set proper Frame of Reference UID matching CT reference
        # Implement dose summation type validation
        
    def _calculate_dose_scaling(self, dose_array):
        # Optimize scaling factor for best precision
        # Follow DICOM standard for dose grid scaling
        
    def _create_multiframe_structure(self, dose_array):
        # Handle large dose arrays with proper frame organization
        # Compress if beneficial for file size
```

**Success Criteria**:
- Create RT Dose files from dose arrays in <10 seconds
- Dose accuracy within 0.1% of input array values
- Generated files compatible with dose analysis tools
- Handle dose grids up to 512³ voxels efficiently

### Phase 3: Plan Creation & Integration (Month 4)

**Objectives**: Complete MVP with RT Plan creation and scikit-rt integration hooks

**Key Deliverables**:
- RT Plan creation with beam geometry
- Basic treatment planning parameter support
- Integration interface for scikit-rt compatibility
- Comprehensive documentation and examples

### Phase 4: Anonymization & Privacy Protection (Month 5)

**Objectives**: Implement comprehensive DICOM anonymization capabilities for clinical data sharing and research

**Key Deliverables**:
- Multi-level anonymization profiles (basic, clinical, research)
- HIPAA-compliant de-identification following DICOM PS 3.15
- Audit logging for anonymization operations
- Batch anonymization for multiple files
- Integration with all RT DICOM types

**Technical Focus**:
```python
# Anonymization Implementation
class DicomAnonymizer:
    def __init__(self, profile='basic'):
        # Load anonymization profile (basic, clinical, research, custom)
        # Initialize audit logger for compliance tracking
        
    def anonymize_dataset(self, dataset, profile=None):
        # Apply anonymization based on DICOM PS 3.15 Application Level Confidentiality Profile
        # Handle special RT-specific tags that must be preserved for clinical validity
        # Generate consistent anonymized UIDs across related objects
        
    def create_anonymization_map(self, datasets):
        # Create consistent anonymization mapping for related DICOM objects
        # Ensure RT references remain valid after anonymization
        
class AnonymizationProfiles:
    BASIC_PROFILE = {
        'remove_patient_identifiers': True,
        'preserve_dates': False,
        'preserve_geometric_data': True,
        'preserve_dose_data': True
    }
    
    CLINICAL_PROFILE = {
        'remove_patient_identifiers': True,
        'preserve_dates': True,  # Preserve relative dates for clinical analysis
        'preserve_geometric_data': True,
        'preserve_dose_data': True,
        'preserve_physician_info': False
    }
    
    RESEARCH_PROFILE = {
        'remove_patient_identifiers': True,
        'preserve_dates': True,
        'preserve_geometric_data': True,
        'preserve_dose_data': True,
        'preserve_institution_info': False,
        'anonymize_study_descriptions': True
    }
```

**Technical Implementation**:
```python
# RT Plan Implementation
class RTPlan(BaseDicomCreator):
    @classmethod 
    def from_beam_config(cls, prescription, beams, **kwargs):
        # Create basic plan structure with fraction scheme
        # Implement beam limiting device sequences
        # Set machine parameters and beam geometry
        # Reference related RT Dose and Structure Set
        
    def _create_fraction_scheme(self, prescription):
        # Handle dose per fraction and number of fractions
        # Set prescription dose reference
        
    def _create_beam_sequence(self, beams):
        # Convert beam configurations to DICOM beam sequence
        # Handle control points and MLC positions (basic)

# Scikit-rt Integration Layer
class ScikitRTInterface:
    @staticmethod
    def to_scikit_rt(pyrt_dicom_object):
        # Convert pyrt-dicom objects to scikit-rt format
        
    @staticmethod  
    def from_scikit_rt(skiart_rt_object):
        # Import from scikit-rt for DICOM creation
```

**Success Criteria**:
- Create basic RT Plans with beam configurations
- Successfully interface with scikit-rt objects
- Complete clinical workflow from data to DICOM files
- Documentation covers all major use cases

**Anonymization Success Criteria**:
- HIPAA-compliant anonymization following DICOM PS 3.15 standards
- Preserve clinical validity of RT data after anonymization
- Maintain DICOM reference relationships in anonymized datasets
- Support batch anonymization of complete treatment plans
- Comprehensive audit logging for compliance requirements

## Validation & Quality Strategy

### Clinical Validation Framework

**Multi-Level Validation** (inspired by PyMedPhys validation patterns):
```python
class ClinicalValidator:
    """Comprehensive clinical validation for safety"""
    
    DOSE_LIMITS = {
        'max_clinical_dose': 30.0,      # Gy, typical high-dose limit
        'max_fraction_dose': 8.0,       # Gy, SBRT maximum
        'min_meaningful_dose': 0.01,    # Gy, minimum clinical relevance
    }
    
    GEOMETRIC_LIMITS = {
        'max_structure_volume': 3000,   # cc, large structure limit
        'min_structure_volume': 0.1,    # cc, minimum meaningful volume
        'max_pixel_spacing': 5.0,       # mm, coarse resolution limit
        'min_pixel_spacing': 0.1,       # mm, fine resolution limit
    }
    
    def validate_dose_array(self, dose_array, dose_units):
        """Validate dose values for clinical reasonableness"""
        
    def validate_structure_geometry(self, mask, structure_name):
        """Validate structure mask geometry and naming"""
        
    def validate_coordinate_consistency(self, objects):
        """Ensure all objects use consistent coordinate systems"""
```

### DICOM Compliance Testing

**Automated Compliance Checking**:
- IOD-specific attribute validation for each modality
- DICOM conformance statement compliance
- Multi-vendor TPS compatibility testing
- Round-trip testing (create → read → validate)

### Performance Benchmarks

**Target Performance Metrics**:
- **CT Series Creation**: <5 seconds for 200 slices
- **RT Structure Creation**: <3 seconds for 20 structures  
- **RT Dose Creation**: <10 seconds for 512³ dose grid
- **RT Plan Creation**: <2 seconds for basic plans
- **Memory Usage**: <1GB for typical clinical datasets

## Integration Strategy

### scikit-rt Integration Design

**Bidirectional Interface**:
```python
# Export pyrt-dicom objects to scikit-rt for analysis
pyrt_struct = prt.RTStructureSet.from_masks(...)
scikit_struct = pyrt_struct.to_scikit_rt()

# Import from scikit-rt for DICOM creation
scikit_dose = skrt.Dose(...)  # From scikit-rt analysis
pyrt_dose = prt.RTDose.from_scikit_rt(scikit_dose)
pyrt_dose.save('dose_from_analysis.dcm')

# Workflow integration
dose_analysis = scikit_dose.calculate_dvh(structure)
modified_dose = dose_analysis.apply_corrections()
final_dicom = prt.RTDose.from_scikit_rt(modified_dose)
```

### PyMedPhys Learning Integration

**Code Study Process**:
1. **Download PyMedPhys repository** to VS Code workspace alongside pyrt-dicom
2. **Analyze _pinnacle module** structure and patterns:
   - `lib/pymedphys/_pinnacle/pinnacle.py` - Main export logic
   - `lib/pymedphys/_dicom/` - DICOM handling patterns
   - UID generation strategies and coordinate transformations
3. **Extract reusable patterns** while improving API design
4. **Test against PyMedPhys outputs** for validation

## Future Enhancements

### Command Line Interface (CLI)
A command-line interface will be developed to provide convenient access to common pyrt-dicom functionality. The CLI will support:
- Batch processing of DICOM file creation
- Simple conversion between different DICOM formats
- Basic validation and verification of DICOM files
- Integration with existing DICOM tools and workflows

## Success Metrics

### Technical Success Criteria

**Functionality Coverage**:
- ✅ Create all 4 RT DICOM types from common data sources
- ✅ Pass DICOM conformance validation for all created files
- ✅ Demonstrate round-trip compatibility (create → read → validate)
- ✅ Handle clinical-scale datasets efficiently

**Quality Standards**:
- ✅ >95% test coverage for core functionality
- ✅ Zero critical validation failures in clinical range tests
- ✅ Compatibility with major TPS vendors (Varian, Elekta, RaySearch)
- ✅ Successful integration demonstrations with scikit-rt

### User Experience Success Criteria

**API Usability**:
- ✅ Common workflows achievable in <5 lines of code
- ✅ Self-documenting code with clear clinical context
- ✅ Helpful error messages with specific guidance
- ✅ Comprehensive examples covering typical use cases

**Adoption Indicators**:
- ✅ 50+ monthly active users within 6 months
- ✅ 5+ community contributions or feature requests
- ✅ Integration by 2+ research groups or clinical departments
- ✅ Positive feedback from medical physics beta testers

### Market Validation Success Criteria

**Ecosystem Integration**:
- ✅ Successful integration with scikit-rt workflows
- ✅ Clear differentiation from existing tools (PyMedPhys, RT-utils)
- ✅ Evidence of reduced custom DICOM scripting in target workflows
- ✅ Foundation established for future comprehensive library development

## Risk Mitigation

### Technical Risks

**DICOM Complexity Management**:
- *Mitigation*: Start with PyMedPhys proven patterns, extensive testing with real data
- *Monitoring*: Track compatibility issues across different TPS vendors

**Performance at Scale**:
- *Mitigation*: Profile early with clinical-scale datasets, implement efficient algorithms
- *Monitoring*: Automated performance regression testing

### Adoption Risks

**Competition from Existing Tools**:
- *Mitigation*: Focus on unique value proposition (complete creation capabilities)
- *Monitoring*: Regular competitive analysis and user feedback collection

**Learning Curve for Medical Physicists**:
- *Mitigation*: Extensive documentation, clinical examples, gradual API disclosure
- *Monitoring*: User support request analysis and onboarding feedback

## Conclusion

**pyrt-dicom** addresses the most critical gap in the Python RT DICOM ecosystem - robust, user-friendly creation of all four core RT DICOM types. By learning from PyMedPhys's proven patterns while focusing specifically on creation workflows, we can deliver a tool that complements the existing ecosystem rather than competing with it.

The phased development approach ensures early validation of core concepts while building toward comprehensive coverage. The planned scikit-rt integration provides a clear path for users to combine analysis capabilities with creation tools, establishing pyrt-dicom as an essential component in the Python medical physics toolkit.

Success will be measured not just by technical capabilities, but by real impact on clinical workflows and the establishment of pyrt-dicom as the standard solution for RT DICOM creation in Python.